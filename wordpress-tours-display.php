<?php
/**
 * WordPress Tours Display Plugin
 * Add this to your theme's functions.php or create as a plugin
 */

// Add shortcode to display tours
add_shortcode('tours_directory', 'display_tours_directory');

function display_tours_directory($atts) {
    // Parse shortcode attributes
    $atts = shortcode_atts(array(
        'api_url' => 'http://localhost:8000/api/tours/export/directory',
        'location' => '',
        'max_price' => '',
        'limit' => 50,
        'show_images' => 'true',
        'columns' => 3
    ), $atts);

    // Fetch tours data
    $tours_data = fetch_tours_data($atts['api_url']);
    
    if (!$tours_data || empty($tours_data['tours'])) {
        return '<div class="tours-error">No tours available at the moment.</div>';
    }

    $tours = $tours_data['tours'];
    
    // Filter tours based on attributes
    if (!empty($atts['location'])) {
        $tours = array_filter($tours, function($tour) use ($atts) {
            return stripos($tour['location'] ?? '', $atts['location']) !== false;
        });
    }
    
    if (!empty($atts['max_price'])) {
        $tours = array_filter($tours, function($tour) use ($atts) {
            return isset($tour['price']) && $tour['price'] <= floatval($atts['max_price']);
        });
    }
    
    // Limit results
    $tours = array_slice($tours, 0, intval($atts['limit']));
    
    // Generate HTML
    ob_start();
    ?>
    <div class="tours-directory">
        <div class="tours-header">
            <h3>Australian Tours Directory</h3>
            <p class="tours-count"><?php echo count($tours); ?> tours available</p>
            <p class="tours-updated">Last updated: <?php echo date('F j, Y g:i A', strtotime($tours_data['last_export'])); ?></p>
        </div>
        
        <div class="tours-grid tours-columns-<?php echo esc_attr($atts['columns']); ?>">
            <?php foreach ($tours as $tour): ?>
                <div class="tour-card">
                    <?php if ($atts['show_images'] === 'true' && !empty($tour['image_url'])): ?>
                        <div class="tour-image">
                            <img src="<?php echo esc_url($tour['image_url']); ?>" 
                                 alt="<?php echo esc_attr($tour['title']); ?>"
                                 loading="lazy">
                        </div>
                    <?php endif; ?>
                    
                    <div class="tour-content">
                        <h4 class="tour-title">
                            <?php if (!empty($tour['tour_url'])): ?>
                                <a href="<?php echo esc_url($tour['tour_url']); ?>" target="_blank" rel="noopener">
                                    <?php echo esc_html($tour['title']); ?>
                                </a>
                            <?php else: ?>
                                <?php echo esc_html($tour['title']); ?>
                            <?php endif; ?>
                        </h4>
                        
                        <?php if (!empty($tour['location'])): ?>
                            <p class="tour-location">
                                <span class="tour-icon">📍</span>
                                <?php echo esc_html($tour['location']); ?>
                            </p>
                        <?php endif; ?>
                        
                        <?php if (!empty($tour['price'])): ?>
                            <p class="tour-price">
                                <span class="tour-icon">💰</span>
                                From $<?php echo number_format($tour['price'], 0); ?>
                            </p>
                        <?php endif; ?>
                        
                        <div class="tour-business">
                            <p class="business-name">
                                <span class="tour-icon">🏢</span>
                                <?php if (!empty($tour['business_website'])): ?>
                                    <a href="<?php echo esc_url($tour['business_website']); ?>" target="_blank" rel="noopener">
                                        <?php echo esc_html($tour['business_name']); ?>
                                    </a>
                                <?php else: ?>
                                    <?php echo esc_html($tour['business_name']); ?>
                                <?php endif; ?>
                            </p>
                        </div>
                        
                        <?php if (!empty($tour['tour_url'])): ?>
                            <div class="tour-actions">
                                <a href="<?php echo esc_url($tour['tour_url']); ?>" 
                                   class="tour-button" 
                                   target="_blank" 
                                   rel="noopener">
                                    View Tour Details
                                </a>
                            </div>
                        <?php endif; ?>
                    </div>
                </div>
            <?php endforeach; ?>
        </div>
        
        <?php if (empty($tours)): ?>
            <div class="tours-empty">
                <p>No tours match your criteria. Try adjusting your filters.</p>
            </div>
        <?php endif; ?>
    </div>
    
    <style>
    .tours-directory {
        max-width: 1200px;
        margin: 0 auto;
        padding: 20px;
    }
    
    .tours-header {
        text-align: center;
        margin-bottom: 30px;
        padding-bottom: 20px;
        border-bottom: 2px solid #eee;
    }
    
    .tours-header h3 {
        color: #333;
        margin-bottom: 10px;
    }
    
    .tours-count {
        font-size: 18px;
        color: #666;
        margin: 5px 0;
    }
    
    .tours-updated {
        font-size: 14px;
        color: #999;
        margin: 5px 0;
    }
    
    .tours-grid {
        display: grid;
        gap: 25px;
        margin-bottom: 30px;
    }
    
    .tours-columns-1 { grid-template-columns: 1fr; }
    .tours-columns-2 { grid-template-columns: repeat(2, 1fr); }
    .tours-columns-3 { grid-template-columns: repeat(3, 1fr); }
    .tours-columns-4 { grid-template-columns: repeat(4, 1fr); }
    
    @media (max-width: 768px) {
        .tours-grid {
            grid-template-columns: 1fr !important;
        }
    }
    
    .tour-card {
        background: white;
        border-radius: 12px;
        box-shadow: 0 4px 6px rgba(0,0,0,0.1);
        overflow: hidden;
        transition: transform 0.3s ease, box-shadow 0.3s ease;
    }
    
    .tour-card:hover {
        transform: translateY(-5px);
        box-shadow: 0 8px 25px rgba(0,0,0,0.15);
    }
    
    .tour-image {
        width: 100%;
        height: 200px;
        overflow: hidden;
    }
    
    .tour-image img {
        width: 100%;
        height: 100%;
        object-fit: cover;
        transition: transform 0.3s ease;
    }
    
    .tour-card:hover .tour-image img {
        transform: scale(1.05);
    }
    
    .tour-content {
        padding: 20px;
    }
    
    .tour-title {
        margin: 0 0 15px 0;
        font-size: 18px;
        line-height: 1.4;
    }
    
    .tour-title a {
        color: #333;
        text-decoration: none;
        transition: color 0.3s ease;
    }
    
    .tour-title a:hover {
        color: #007cba;
    }
    
    .tour-location,
    .tour-price,
    .business-name {
        margin: 8px 0;
        font-size: 14px;
        color: #666;
        display: flex;
        align-items: center;
        gap: 8px;
    }
    
    .tour-icon {
        font-size: 16px;
    }
    
    .tour-price {
        font-weight: bold;
        color: #2c5aa0;
    }
    
    .business-name a {
        color: #666;
        text-decoration: none;
    }
    
    .business-name a:hover {
        color: #007cba;
    }
    
    .tour-actions {
        margin-top: 15px;
        padding-top: 15px;
        border-top: 1px solid #eee;
    }
    
    .tour-button {
        display: inline-block;
        background: #007cba;
        color: white;
        padding: 10px 20px;
        border-radius: 6px;
        text-decoration: none;
        font-size: 14px;
        font-weight: bold;
        transition: background 0.3s ease;
    }
    
    .tour-button:hover {
        background: #005a87;
        color: white;
    }
    
    .tours-error,
    .tours-empty {
        text-align: center;
        padding: 40px 20px;
        background: #f9f9f9;
        border-radius: 8px;
        color: #666;
    }
    </style>
    <?php
    
    return ob_get_clean();
}

function fetch_tours_data($api_url) {
    // Use WordPress HTTP API
    $response = wp_remote_get($api_url, array(
        'timeout' => 30,
        'headers' => array(
            'Accept' => 'application/json',
        )
    ));
    
    if (is_wp_error($response)) {
        error_log('Tours API Error: ' . $response->get_error_message());
        return false;
    }
    
    $body = wp_remote_retrieve_body($response);
    $data = json_decode($body, true);
    
    if (json_last_error() !== JSON_ERROR_NONE) {
        error_log('Tours JSON Error: ' . json_last_error_msg());
        return false;
    }
    
    return $data;
}

// Add admin menu for tours settings
add_action('admin_menu', 'tours_directory_admin_menu');

function tours_directory_admin_menu() {
    add_options_page(
        'Tours Directory Settings',
        'Tours Directory',
        'manage_options',
        'tours-directory',
        'tours_directory_admin_page'
    );
}

function tours_directory_admin_page() {
    if (isset($_POST['submit'])) {
        update_option('tours_api_url', sanitize_url($_POST['api_url']));
        echo '<div class="notice notice-success"><p>Settings saved!</p></div>';
    }
    
    $api_url = get_option('tours_api_url', 'http://localhost:8000/api/tours/export/directory');
    ?>
    <div class="wrap">
        <h1>Tours Directory Settings</h1>
        <form method="post">
            <table class="form-table">
                <tr>
                    <th scope="row">API URL</th>
                    <td>
                        <input type="url" name="api_url" value="<?php echo esc_attr($api_url); ?>" class="regular-text" />
                        <p class="description">URL to your tours API endpoint</p>
                    </td>
                </tr>
            </table>
            <?php submit_button(); ?>
        </form>
        
        <h2>Usage Examples</h2>
        <p><strong>Basic usage:</strong></p>
        <code>[tours_directory]</code>
        
        <p><strong>With filters:</strong></p>
        <code>[tours_directory location="Adelaide" max_price="500" limit="20"]</code>
        
        <p><strong>Custom layout:</strong></p>
        <code>[tours_directory columns="2" show_images="false"]</code>
        
        <h3>Available Parameters:</h3>
        <ul>
            <li><code>api_url</code> - Custom API endpoint</li>
            <li><code>location</code> - Filter by location</li>
            <li><code>max_price</code> - Maximum price filter</li>
            <li><code>limit</code> - Number of tours to show (default: 50)</li>
            <li><code>show_images</code> - Show tour images (true/false)</li>
            <li><code>columns</code> - Grid columns (1-4, default: 3)</li>
        </ul>
    </div>
    <?php
}
?>
