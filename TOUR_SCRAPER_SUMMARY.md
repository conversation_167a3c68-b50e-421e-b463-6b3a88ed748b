# Tour Scraper App - Integration Summary

## 🎯 What This App Does
- **Discovers** Australian tour operators automatically via web search
- **Scrapes** tour listings from their websites (currently extracting 65+ tours)
- **Stores** tour data in PostgreSQL database with business info
- **Provides** REST API access to the data
- **Runs continuously** via Docker containers with scheduled scraping

## 📊 Current Data Available
- **65 tours** from 3 active sources:
  - **SA Eco Tours**: 9 eco tours (Flinders Ranges, Lake Eyre, etc.)
  - **Discover Australia**: 11 experiences (Urban, Regional, Multi-day)
  - **Australian Tours**: 45 tours (state-wide coverage)
- **Data includes**: Title, Location, Business Name, Tour URL, Business Website
- **Price handling**: Only shows when available, otherwise omitted
- **Locations**: Adelaide, Melbourne, Sydney, Brisbane, Perth, Darwin, Flinders Ranges, etc.

## 🔄 Automation Options for Your Directory App

### Option 1: Simple HTTP Polling ⭐ (Recommended - Easiest)
```
Your App → HTTP GET → http://localhost:8000/api/tours/export/directory
```
- **How**: Schedule your app to call the API every hour/daily
- **Pros**: Simple, reliable, you control timing
- **Cons**: May fetch same data repeatedly
- **Best for**: Most directory apps

### Option 2: Webhook Notifications (Most Efficient)
```
Scraper finds new tours → HTTP POST → Your App's webhook endpoint
```
- **How**: Scraper pushes notifications when new tours found
- **Pros**: Real-time updates, no wasted calls
- **Cons**: Requires webhook endpoint in your app
- **Best for**: Apps that need immediate updates

### Option 3: Database Direct Access (Fastest)
```
Your App → Direct SQL → PostgreSQL Database
```
- **How**: Connect directly to the scraper's database
- **Pros**: Fastest, most flexible queries
- **Cons**: Tight coupling, requires PostgreSQL access
- **Best for**: Apps using PostgreSQL

### Option 4: Export Files (Simplest)
```
Scraper → Generates JSON/CSV → Your App reads files
```
- **How**: Scraper exports data to files, your app imports them
- **Pros**: No API dependencies, works offline
- **Cons**: Less real-time, file management needed
- **Best for**: Batch processing apps

## 🏗️ App Architecture
```
Discovery Worker → Finds tour operator websites
     ↓
Scraper Worker → Extracts tour data every 5 minutes
     ↓
PostgreSQL DB → Stores tours + businesses
     ↓
FastAPI Backend → Provides REST endpoints
     ↓
Your Directory App → Consumes tour data
```

## 📡 Available API Endpoints
- `GET /api/tours/export/directory` - Clean export format (no auth needed)
- `GET /api/tours` - Full tours API (requires API key)
- `GET /api/businesses` - Business information
- Management UI at `http://localhost:8000/ui/manage`

## 🚀 Current Status
- ✅ **Working**: Scraping 65+ tours successfully
- ✅ **Stable**: Running in Docker containers
- ✅ **Growing**: Discovers new tour operators automatically
- ✅ **Clean data**: Proper filtering, location detection, duplicate removal

## 💡 Recommendation
Start with **Option 1 (HTTP Polling)** - it's the most straightforward and reliable for most directory apps.

## 📋 Sample API Response
```json
{
  "total_tours": 65,
  "last_export": "2025-08-26T07:29:14.838731",
  "tours": [
    {
      "id": 1,
      "title": "3-Day Flinders Ranges & Outback Small Group Eco Tour",
      "location": "Flinders Ranges",
      "tour_url": "https://saecotours.com.au/tours/3-day-flinders-ranges-outback-small-group-4wd-atv-eco-tour/",
      "business_name": "SA Eco Tours",
      "business_website": "https://saecotours.com.au/tours",
      "last_updated": "2025-08-26T07:29:14.838731"
    }
  ]
}
```

## 🔧 Implementation Examples

### Python (Option 1 - HTTP Polling)
```python
import requests
import schedule
import time

def sync_tours():
    response = requests.get('http://localhost:8000/api/tours/export/directory')
    data = response.json()
    
    print(f"Fetched {data['total_tours']} tours")
    for tour in data['tours']:
        save_to_directory(tour)

# Run every hour
schedule.every().hour.do(sync_tours)
```

### Node.js (Option 1 - HTTP Polling)
```javascript
const axios = require('axios');
const cron = require('node-cron');

async function syncTours() {
    const response = await axios.get('http://localhost:8000/api/tours/export/directory');
    const data = response.data;
    
    console.log(`Fetched ${data.total_tours} tours`);
    for (const tour of data.tours) {
        await saveToDirectory(tour);
    }
}

// Run every hour
cron.schedule('0 * * * *', syncTours);
```

### SQL Direct Access (Option 3)
```sql
-- Connect to PostgreSQL database directly
SELECT 
    b.name as business_name,
    b.website_url,
    t.title,
    t.location,
    t.url as tour_url,
    t.last_updated
FROM tours t
JOIN businesses b ON t.business_id = b.id
WHERE t.location IS NOT NULL
ORDER BY t.last_updated DESC;
```

---

**Contact**: This scraper app is running at `http://localhost:8000` with 65+ Australian tours ready for integration.
