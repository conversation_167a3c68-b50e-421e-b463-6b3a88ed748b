/**
 * JavaScript Tours Display - Client-side loading
 * Add this to your WordPress theme or use in a Custom HTML block
 */

class ToursDirectory {
    constructor(containerId, options = {}) {
        this.container = document.getElementById(containerId);
        this.options = {
            apiUrl: options.apiUrl || 'http://localhost:8000/api/tours/export/directory',
            limit: options.limit || 20,
            showImages: options.showImages !== false,
            columns: options.columns || 3,
            location: options.location || '',
            maxPrice: options.maxPrice || null,
            ...options
        };
        
        this.init();
    }
    
    async init() {
        this.showLoading();
        try {
            const data = await this.fetchTours();
            this.renderTours(data);
        } catch (error) {
            this.showError('Failed to load tours: ' + error.message);
        }
    }
    
    async fetchTours() {
        const response = await fetch(this.options.apiUrl);
        if (!response.ok) {
            throw new Error(`HTTP ${response.status}: ${response.statusText}`);
        }
        return await response.json();
    }
    
    filterTours(tours) {
        let filtered = tours;
        
        if (this.options.location) {
            filtered = filtered.filter(tour => 
                tour.location && tour.location.toLowerCase().includes(this.options.location.toLowerCase())
            );
        }
        
        if (this.options.maxPrice) {
            filtered = filtered.filter(tour => 
                tour.price && tour.price <= this.options.maxPrice
            );
        }
        
        return filtered.slice(0, this.options.limit);
    }
    
    renderTours(data) {
        if (!data.tours || data.tours.length === 0) {
            this.showError('No tours available');
            return;
        }
        
        const tours = this.filterTours(data.tours);
        
        const html = `
            <div class="tours-directory-js">
                <div class="tours-header">
                    <h3>Australian Tours Directory</h3>
                    <p class="tours-count">${tours.length} tours available</p>
                    <p class="tours-updated">Last updated: ${new Date(data.last_export).toLocaleDateString()}</p>
                </div>
                
                <div class="tours-grid" style="grid-template-columns: repeat(${this.options.columns}, 1fr);">
                    ${tours.map(tour => this.renderTourCard(tour)).join('')}
                </div>
            </div>
        `;
        
        this.container.innerHTML = html;
        this.addStyles();
    }
    
    renderTourCard(tour) {
        return `
            <div class="tour-card">
                ${this.options.showImages && tour.image_url ? `
                    <div class="tour-image">
                        <img src="${tour.image_url}" alt="${tour.title}" loading="lazy">
                    </div>
                ` : ''}
                
                <div class="tour-content">
                    <h4 class="tour-title">
                        ${tour.tour_url ? `
                            <a href="${tour.tour_url}" target="_blank" rel="noopener">
                                ${tour.title}
                            </a>
                        ` : tour.title}
                    </h4>
                    
                    ${tour.location ? `
                        <p class="tour-location">
                            <span class="tour-icon">📍</span>
                            ${tour.location}
                        </p>
                    ` : ''}
                    
                    ${tour.price ? `
                        <p class="tour-price">
                            <span class="tour-icon">💰</span>
                            From $${tour.price.toLocaleString()}
                        </p>
                    ` : ''}
                    
                    <div class="tour-business">
                        <p class="business-name">
                            <span class="tour-icon">🏢</span>
                            ${tour.business_website ? `
                                <a href="${tour.business_website}" target="_blank" rel="noopener">
                                    ${tour.business_name}
                                </a>
                            ` : tour.business_name}
                        </p>
                    </div>
                    
                    ${tour.tour_url ? `
                        <div class="tour-actions">
                            <a href="${tour.tour_url}" class="tour-button" target="_blank" rel="noopener">
                                View Tour Details
                            </a>
                        </div>
                    ` : ''}
                </div>
            </div>
        `;
    }
    
    showLoading() {
        this.container.innerHTML = `
            <div class="tours-loading">
                <p>Loading tours...</p>
                <div class="loading-spinner"></div>
            </div>
        `;
    }
    
    showError(message) {
        this.container.innerHTML = `
            <div class="tours-error">
                <p>${message}</p>
            </div>
        `;
    }
    
    addStyles() {
        if (document.getElementById('tours-directory-styles')) return;
        
        const styles = `
            <style id="tours-directory-styles">
            .tours-directory-js {
                max-width: 1200px;
                margin: 0 auto;
                padding: 20px;
            }
            
            .tours-header {
                text-align: center;
                margin-bottom: 30px;
                padding-bottom: 20px;
                border-bottom: 2px solid #eee;
            }
            
            .tours-grid {
                display: grid;
                gap: 25px;
                margin-bottom: 30px;
            }
            
            @media (max-width: 768px) {
                .tours-grid {
                    grid-template-columns: 1fr !important;
                }
            }
            
            .tour-card {
                background: white;
                border-radius: 12px;
                box-shadow: 0 4px 6px rgba(0,0,0,0.1);
                overflow: hidden;
                transition: transform 0.3s ease, box-shadow 0.3s ease;
            }
            
            .tour-card:hover {
                transform: translateY(-5px);
                box-shadow: 0 8px 25px rgba(0,0,0,0.15);
            }
            
            .tour-image {
                width: 100%;
                height: 200px;
                overflow: hidden;
            }
            
            .tour-image img {
                width: 100%;
                height: 100%;
                object-fit: cover;
            }
            
            .tour-content {
                padding: 20px;
            }
            
            .tour-title {
                margin: 0 0 15px 0;
                font-size: 18px;
                line-height: 1.4;
            }
            
            .tour-title a {
                color: #333;
                text-decoration: none;
            }
            
            .tour-title a:hover {
                color: #007cba;
            }
            
            .tour-location,
            .tour-price,
            .business-name {
                margin: 8px 0;
                font-size: 14px;
                color: #666;
                display: flex;
                align-items: center;
                gap: 8px;
            }
            
            .tour-price {
                font-weight: bold;
                color: #2c5aa0;
            }
            
            .tour-button {
                display: inline-block;
                background: #007cba;
                color: white;
                padding: 10px 20px;
                border-radius: 6px;
                text-decoration: none;
                font-size: 14px;
                font-weight: bold;
                transition: background 0.3s ease;
            }
            
            .tour-button:hover {
                background: #005a87;
                color: white;
            }
            
            .tours-loading,
            .tours-error {
                text-align: center;
                padding: 40px 20px;
                background: #f9f9f9;
                border-radius: 8px;
                color: #666;
            }
            
            .loading-spinner {
                border: 3px solid #f3f3f3;
                border-top: 3px solid #007cba;
                border-radius: 50%;
                width: 30px;
                height: 30px;
                animation: spin 1s linear infinite;
                margin: 20px auto;
            }
            
            @keyframes spin {
                0% { transform: rotate(0deg); }
                100% { transform: rotate(360deg); }
            }
            </style>
        `;
        
        document.head.insertAdjacentHTML('beforeend', styles);
    }
}

// Usage examples:

// Basic usage
// new ToursDirectory('tours-container');

// With options
// new ToursDirectory('tours-container', {
//     apiUrl: 'http://your-domain.com:8000/api/tours/export/directory',
//     limit: 15,
//     columns: 2,
//     location: 'Adelaide',
//     maxPrice: 500
// });

// Auto-initialize on page load
document.addEventListener('DOMContentLoaded', function() {
    // Look for containers with data attributes
    const containers = document.querySelectorAll('[data-tours-directory]');
    
    containers.forEach(container => {
        const options = {
            apiUrl: container.dataset.apiUrl,
            limit: parseInt(container.dataset.limit) || 20,
            columns: parseInt(container.dataset.columns) || 3,
            location: container.dataset.location,
            maxPrice: parseFloat(container.dataset.maxPrice) || null,
            showImages: container.dataset.showImages !== 'false'
        };
        
        new ToursDirectory(container.id, options);
    });
});
