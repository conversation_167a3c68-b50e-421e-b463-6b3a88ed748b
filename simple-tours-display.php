<?php
/**
 * Simple Tours Display - Add to functions.php
 * Place tours.json in wp-content/uploads/ directory
 */

add_shortcode('tours', 'display_tours_simple');

function display_tours_simple($atts) {
    $atts = shortcode_atts(array(
        'limit' => 20,
        'columns' => 3
    ), $atts);

    // Get JSON file from uploads directory
    $upload_dir = wp_upload_dir();
    $json_file = $upload_dir['basedir'] . '/tours.json';
    
    if (!file_exists($json_file)) {
        return '<p>Tours file not found. Please upload tours.json to wp-content/uploads/</p>';
    }
    
    $json_data = file_get_contents($json_file);
    $data = json_decode($json_data, true);
    
    if (!$data || empty($data['tours'])) {
        return '<p>No tours data available.</p>';
    }
    
    $tours = array_slice($data['tours'], 0, intval($atts['limit']));
    
    ob_start();
    ?>
    <div class="tours-display">
        <div class="tours-header">
            <h3>Australian Tours</h3>
            <p><?php echo count($tours); ?> tours available</p>
        </div>
        
        <div class="tours-grid tours-cols-<?php echo $atts['columns']; ?>">
            <?php foreach ($tours as $tour): ?>
                <div class="tour-card">
                    <?php if (!empty($tour['image_url'])): ?>
                        <img src="<?php echo esc_url($tour['image_url']); ?>" alt="<?php echo esc_attr($tour['title']); ?>">
                    <?php endif; ?>
                    
                    <div class="tour-info">
                        <h4>
                            <?php if (!empty($tour['tour_url'])): ?>
                                <a href="<?php echo esc_url($tour['tour_url']); ?>" target="_blank">
                                    <?php echo esc_html($tour['title']); ?>
                                </a>
                            <?php else: ?>
                                <?php echo esc_html($tour['title']); ?>
                            <?php endif; ?>
                        </h4>
                        
                        <?php if (!empty($tour['location'])): ?>
                            <p>📍 <?php echo esc_html($tour['location']); ?></p>
                        <?php endif; ?>
                        
                        <?php if (!empty($tour['price'])): ?>
                            <p class="price">💰 From $<?php echo number_format($tour['price'], 0); ?></p>
                        <?php endif; ?>
                        
                        <p class="business">🏢 <?php echo esc_html($tour['business_name']); ?></p>
                        
                        <?php if (!empty($tour['tour_url'])): ?>
                            <a href="<?php echo esc_url($tour['tour_url']); ?>" class="tour-btn" target="_blank">View Details</a>
                        <?php endif; ?>
                    </div>
                </div>
            <?php endforeach; ?>
        </div>
    </div>
    
    <style>
    .tours-display {
        max-width: 1200px;
        margin: 20px auto;
        padding: 20px;
    }
    .tours-header {
        text-align: center;
        margin-bottom: 30px;
    }
    .tours-grid {
        display: grid;
        gap: 20px;
        margin-bottom: 20px;
    }
    .tours-cols-1 { grid-template-columns: 1fr; }
    .tours-cols-2 { grid-template-columns: repeat(2, 1fr); }
    .tours-cols-3 { grid-template-columns: repeat(3, 1fr); }
    .tours-cols-4 { grid-template-columns: repeat(4, 1fr); }
    
    @media (max-width: 768px) {
        .tours-grid { grid-template-columns: 1fr !important; }
    }
    
    .tour-card {
        background: white;
        border-radius: 8px;
        box-shadow: 0 2px 8px rgba(0,0,0,0.1);
        overflow: hidden;
        transition: transform 0.3s ease;
    }
    .tour-card:hover {
        transform: translateY(-5px);
    }
    .tour-card img {
        width: 100%;
        height: 200px;
        object-fit: cover;
    }
    .tour-info {
        padding: 20px;
    }
    .tour-info h4 {
        margin: 0 0 10px 0;
        font-size: 18px;
    }
    .tour-info h4 a {
        color: #333;
        text-decoration: none;
    }
    .tour-info h4 a:hover {
        color: #007cba;
    }
    .tour-info p {
        margin: 8px 0;
        color: #666;
        font-size: 14px;
    }
    .tour-info .price {
        color: #2c5aa0;
        font-weight: bold;
    }
    .tour-btn {
        display: inline-block;
        background: #007cba;
        color: white;
        padding: 10px 20px;
        border-radius: 5px;
        text-decoration: none;
        font-size: 14px;
        margin-top: 10px;
    }
    .tour-btn:hover {
        background: #005a87;
        color: white;
    }
    </style>
    <?php
    
    return ob_get_clean();
}
?>
