from fastapi import APIRouter, Request, Depends, Form, HTTPException
from fastapi.responses import HTMLResponse, RedirectResponse
from fastapi.templating import Jinja2Templates
from sqlalchemy.orm import Session
import asyncio
import os
from datetime import datetime

from .db import get_db
from . import models


templates = Jinja2Templates(directory="app/templates")
router = APIRouter()


@router.get("/ui", response_class=HTMLResponse)
def ui_form(request: Request, db: Session = Depends(get_db)):
    sources = db.query(models.ScrapeSource).order_by(models.ScrapeSource.created_at.desc()).limit(50).all()
    businesses = db.query(models.Business).order_by(models.Business.name.asc()).all()
    return templates.TemplateResponse("ui_form.html", {"request": request, "sources": sources, "businesses": businesses})


@router.post("/ui")
async def ui_submit(request: Request, db: Session = Depends(get_db)):
    form = await request.form()
    business_name = str(form.get("business_name", "")).strip()
    start_url = str(form.get("start_url", "")).strip()
    if business_name and start_url:
        # Create source with immediate processing (5 minutes), then 24 hours
        source = models.ScrapeSource(
            business_name=business_name, 
            start_url=start_url,
            schedule_minutes=5  # Process immediately, then every 24 hours
        )
        db.add(source)
        db.commit()
        
        # After first run, update to 24 hours
        # This will be handled by the scheduler after first processing
    return RedirectResponse(url="/ui", status_code=303)


@router.get("/ui/manage", response_class=HTMLResponse)
def manage_sources(request: Request, db: Session = Depends(get_db)):
    # Get all sources and sort: active first, then alphabetically by business name
    sources = db.query(models.ScrapeSource).all()
    sources.sort(key=lambda s: (not s.is_active, s.business_name.lower()))
    return templates.TemplateResponse("manage_sources.html", {"request": request, "sources": sources})


@router.get("/ui/businesses", response_class=HTMLResponse)
def list_businesses(request: Request, db: Session = Depends(get_db)):
    businesses = db.query(models.Business).all()

    # Get tour counts and tours for each business
    business_data = []
    for business in businesses:
        tours = db.query(models.Tour).filter(models.Tour.business_id == business.id).order_by(models.Tour.title.asc()).all()
        tour_count = len(tours)
        active_tour_count = len([t for t in tours if t.is_active])
        source_count = db.query(models.ScrapeSource).filter(models.ScrapeSource.business_id == business.id).count()

        # Determine if business is "active" (has active tours or active sources)
        has_active_sources = db.query(models.ScrapeSource).filter(
            models.ScrapeSource.business_id == business.id,
            models.ScrapeSource.is_active == True
        ).count() > 0
        is_business_active = active_tour_count > 0 or has_active_sources

        business_data.append({
            'business': business,
            'tours': tours,
            'tour_count': tour_count,
            'active_tour_count': active_tour_count,
            'source_count': source_count,
            'is_active': is_business_active
        })

    # Sort: active businesses first, then alphabetically by name
    business_data.sort(key=lambda b: (not b['is_active'], b['business'].name.lower()))

    return templates.TemplateResponse("businesses.html", {"request": request, "business_data": business_data})


@router.post("/ui/manage/{source_id}/delete")
async def delete_source_ui(source_id: int, db: Session = Depends(get_db)):
    try:
        source = db.query(models.ScrapeSource).get(source_id)
        if source:
            # Simply delete the source - let the database handle cascading
            db.delete(source)
            db.commit()
            print(f"Successfully deleted source {source_id}")
    except Exception as e:
        db.rollback()
        print(f"Error deleting source {source_id}: {e}")
        # Continue to redirect even if there's an error

    return RedirectResponse(url="/ui/manage", status_code=303)


@router.post("/ui/manage/{source_id}/toggle")
async def toggle_source_ui(source_id: int, db: Session = Depends(get_db)):
    source = db.query(models.ScrapeSource).get(source_id)
    if source:
        source.is_active = not source.is_active
        db.add(source)
        db.commit()
    return RedirectResponse(url="/ui/manage", status_code=303)


@router.post("/ui/manage/{source_id}/edit")
async def edit_source_ui(
    source_id: int,
    request: Request,
    db: Session = Depends(get_db)
):
    form = await request.form()
    source = db.query(models.ScrapeSource).get(source_id)
    if source:
        business_name = str(form.get("business_name", "")).strip()
        start_url = str(form.get("start_url", "")).strip()
        schedule_minutes = form.get("schedule_minutes")

        if business_name:
            source.business_name = business_name
        if start_url:
            source.start_url = start_url
        if schedule_minutes:
            try:
                source.schedule_minutes = int(schedule_minutes)
            except ValueError:
                pass  # Keep existing value if invalid

        db.add(source)
        db.commit()
    return RedirectResponse(url="/ui/manage", status_code=303)


@router.post("/ui/manage/{source_id}/scrape-now")
async def scrape_source_now_ui(source_id: int, db: Session = Depends(get_db)):
    """Trigger immediate scrape for a specific source"""
    try:
        source = db.query(models.ScrapeSource).get(source_id)
        if not source:
            raise HTTPException(status_code=404, detail="Source not found")

        # Set last_run_at to far past to make it "due" for immediate processing
        source.last_run_at = datetime(1970, 1, 1)
        db.add(source)
        db.commit()

        # Import and run the scraper job for just this source
        from .worker.scheduler import process_sources_job
        await process_sources_job(db)

    except Exception as e:
        print(f"Error running scrape for source {source_id}: {e}")

    return RedirectResponse(url="/ui/manage", status_code=303)


@router.post("/ui/manage/run-discovery")
async def run_discovery_ui(db: Session = Depends(get_db)):
    """Run discovery to find new tour companies"""
    try:
        # Check if SERPAPI_KEY is configured
        if not os.getenv("SERPAPI_KEY"):
            raise HTTPException(status_code=400, detail="SERPAPI_KEY not configured")

        # Import and run discovery
        from .worker.discovery import discover_au_tour_companies
        max_pages = int(os.getenv("DISCOVERY_MAX_PAGES", "3"))
        created_count = await discover_au_tour_companies(db, max_pages=max_pages)

        print(f"Discovery completed: {created_count} new sources created")

    except Exception as e:
        print(f"Error running discovery: {e}")

    return RedirectResponse(url="/ui/manage", status_code=303)


@router.post("/ui/manage/scrape-all")
async def scrape_all_sources_ui(db: Session = Depends(get_db)):
    """UI endpoint to scrape all active sources with change tracking"""
    try:
        # Import required modules
        from .worker.scraper import parse_example, fetch_html
        from .worker.scheduler import _parse_price_to_float

        # Get all active sources
        active_sources = db.query(models.ScrapeSource).filter(
            models.ScrapeSource.is_active == True
        ).all()

        if not active_sources:
            return RedirectResponse(url="/ui/manage?message=No active sources found", status_code=303)

        # Create unique session ID for this scrape run
        scrape_session = datetime.now().strftime("%Y-%m-%d %H:%M:%S")

        total_tours_created = 0
        total_tours_updated = 0
        sources_processed = 0
        errors = []

        for source in active_sources:
            try:
                print(f"UI Scraping source {source.id}: {source.business_name} - {source.start_url}")

                # Fetch and parse the website
                html = await fetch_html(source.start_url)
                tour_items = list(parse_example(html, source.start_url))

                print(f"Found {len(tour_items)} tour items for {source.business_name}")

                # Get or create business
                business = None
                if source.business_id:
                    business = db.query(models.Business).get(source.business_id)

                if not business:
                    # Create business if it doesn't exist
                    business = models.Business(
                        name=source.business_name,
                        website_url=source.start_url,
                        category="Tours"
                    )
                    db.add(business)
                    db.commit()
                    db.refresh(business)

                    # Link source to business
                    source.business_id = business.id
                    db.add(source)

                # Process tour items with change tracking
                tours_created = 0
                tours_updated = 0
                now = datetime.now()

                for item in tour_items:
                    # Check if tour already exists
                    existing_tour = db.query(models.Tour).filter(
                        models.Tour.business_id == business.id,
                        models.Tour.title == item.title,
                        models.Tour.start_date.is_(None)
                    ).first()

                    if existing_tour:
                        # Track changes for existing tour
                        changes_made = False

                        # Check each field for changes
                        if item.location and item.location != existing_tour.location:
                            db.add(models.TourChange(
                                tour_id=existing_tour.id,
                                business_name=business.name,
                                tour_title=existing_tour.title,
                                field_name="location",
                                old_value=existing_tour.location,
                                new_value=item.location,
                                change_type="updated",
                                scrape_session=scrape_session
                            ))
                            existing_tour.location = item.location
                            changes_made = True

                        new_price = _parse_price_to_float(item.price)
                        if new_price and new_price != existing_tour.price:
                            db.add(models.TourChange(
                                tour_id=existing_tour.id,
                                business_name=business.name,
                                tour_title=existing_tour.title,
                                field_name="price",
                                old_value=str(existing_tour.price) if existing_tour.price else None,
                                new_value=str(new_price),
                                change_type="updated",
                                scrape_session=scrape_session
                            ))
                            existing_tour.price = new_price
                            changes_made = True

                        if item.url and item.url != existing_tour.url:
                            db.add(models.TourChange(
                                tour_id=existing_tour.id,
                                business_name=business.name,
                                tour_title=existing_tour.title,
                                field_name="url",
                                old_value=existing_tour.url,
                                new_value=item.url,
                                change_type="updated",
                                scrape_session=scrape_session
                            ))
                            existing_tour.url = item.url
                            changes_made = True

                        if item.image_url and item.image_url != existing_tour.image_url:
                            db.add(models.TourChange(
                                tour_id=existing_tour.id,
                                business_name=business.name,
                                tour_title=existing_tour.title,
                                field_name="image_url",
                                old_value=existing_tour.image_url,
                                new_value=item.image_url,
                                change_type="updated",
                                scrape_session=scrape_session
                            ))
                            existing_tour.image_url = item.image_url
                            changes_made = True

                        if changes_made:
                            existing_tour.last_updated = now
                            tours_updated += 1
                    else:
                        # Create new tour and track creation
                        new_tour = models.Tour(
                            business_id=business.id,
                            title=item.title or "Untitled",
                            location=item.location,
                            start_date=None,
                            end_date=None,
                            price=_parse_price_to_float(item.price),
                            url=item.url,
                            image_url=item.image_url,
                            last_updated=now
                        )
                        db.add(new_tour)
                        db.commit()
                        db.refresh(new_tour)

                        # Track tour creation
                        db.add(models.TourChange(
                            tour_id=new_tour.id,
                            business_name=business.name,
                            tour_title=new_tour.title,
                            field_name="tour_created",
                            old_value=None,
                            new_value="New tour created",
                            change_type="created",
                            scrape_session=scrape_session
                        ))
                        tours_created += 1

                # Update source last run time
                source.last_run_at = now
                db.add(source)

                total_tours_created += tours_created
                total_tours_updated += tours_updated
                sources_processed += 1

            except Exception as e:
                error_msg = f"Error scraping {source.business_name}: {str(e)}"
                print(error_msg)
                errors.append(error_msg)
                continue

        # Commit all changes
        db.commit()

        message = f"Scraping completed! Processed {sources_processed}/{len(active_sources)} sources. Created {total_tours_created} tours, updated {total_tours_updated} tours."
        if errors:
            message += f" {len(errors)} errors occurred."

        print(f"UI Scrape-all completed: {message}")
        return RedirectResponse(url=f"/ui/changes?message={message}", status_code=303)

    except Exception as e:
        error_msg = f"Error during scrape-all: {str(e)}"
        print(error_msg)
        return RedirectResponse(url=f"/ui/manage?error={error_msg}", status_code=303)


@router.post("/ui/businesses/{business_id}/tours/{tour_id}/toggle")
async def toggle_tour_active_ui(business_id: int, tour_id: int, db: Session = Depends(get_db)):
    """Toggle tour active status and sync with Firebase"""
    try:
        tour = db.query(models.Tour).filter(
            models.Tour.id == tour_id,
            models.Tour.business_id == business_id
        ).first()

        if tour:
            old_status = tour.is_active
            tour.is_active = not tour.is_active
            db.add(tour)
            db.commit()

            # Sync with Firebase
            from .firebase_service import firebase_service
            if tour.is_active:
                # Tour was reactivated - sync it to Firebase
                firebase_service.sync_tour_to_firebase(tour)
                print(f"Tour {tour_id} activated and synced to Firebase: {tour.title}")
            else:
                # Tour was deactivated - remove it from Firebase
                firebase_service.remove_tour_from_firebase(tour)
                print(f"Tour {tour_id} deactivated and removed from Firebase: {tour.title}")

    except Exception as e:
        print(f"Error toggling tour {tour_id}: {e}")

    return RedirectResponse(url="/ui/businesses", status_code=303)


@router.post("/ui/tours/{tour_id}/remove-image")
def remove_tour_image(tour_id: int, db: Session = Depends(get_db)):
    """Remove image from a specific tour"""
    tour = db.query(models.Tour).get(tour_id)
    if not tour:
        return RedirectResponse(url="/ui/businesses?error=Tour not found", status_code=303)

    tour.image_url = None
    db.add(tour)
    db.commit()

    return RedirectResponse(url="/ui/businesses?message=Image removed", status_code=303)


@router.get("/ui/changes", response_class=HTMLResponse)
def view_tour_changes(request: Request, db: Session = Depends(get_db)):
    """View detailed tour changes from recent scrapes"""
    # Get all changes, grouped by scrape session
    changes = db.query(models.TourChange).order_by(
        models.TourChange.scrape_session.desc(),
        models.TourChange.created_at.desc()
    ).limit(1000).all()  # Limit to recent 1000 changes

    # Group changes by session
    change_sessions = {}
    sessions = []

    for change in changes:
        session = change.scrape_session
        if session not in change_sessions:
            change_sessions[session] = []
            sessions.append(session)
        change_sessions[session].append(change)

    return templates.TemplateResponse("tour_changes.html", {
        "request": request,
        "change_sessions": change_sessions,
        "sessions": sessions
    })


@router.post("/ui/changes/clear")
def clear_tour_changes(db: Session = Depends(get_db)):
    """Clear all tour change history"""
    db.query(models.TourChange).delete()
    db.commit()
    return RedirectResponse(url="/ui/changes?message=History cleared", status_code=303)


