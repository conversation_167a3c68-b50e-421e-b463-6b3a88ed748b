# B2B Data Exchange Platform (Tours example)

A FastAPI + Postgres/SQLite backend providing a central hub for businesses to share structured data (e.g., tours). Includes a basic worker skeleton for scraping and a simple API key guard.

## Quick start (no Docker)

1. Python 3.11+
2. Create virtualenv and install deps:

```bash
cd backend
python -m venv .venv
. .venv/Scripts/activate  # Windows PowerShell: .venv\Scripts\Activate.ps1
pip install -r requirements.txt
```

3. Run API (defaults to SQLite `app.db` in repository root):

```bash
uvicorn app.main:app --app-dir backend --reload
```

- Visit `http://localhost:8000/docs`.
- Set `API_KEY` env var to require auth; otherwise, endpoints are open.

## With Docker

Install Docker Desktop, then from repository root:

```bash
# Ensure Docker is installed and available in PATH
docker compose up -d --build
```

The API is on `http://localhost:8000`.

## Environment variables

- `DATABASE_URL`: e.g., `postgresql+psycopg2://appuser:apppass@db:5432/appdb` (Docker) or `sqlite:///./app.db` (default local)
- `API_KEY`: if set, endpoints require `X-API-Key` header
- `WORKER_INTERVAL_SECONDS`: how often the worker scans sources (default 300)
- `SERPAPI_KEY`: SerpAPI key to enable discovery
- `DISCOVERY_MAX_PAGES`: web search pages per cycle (default 1)
- `DISCOVERY_SCHEDULE_MINUTES`: default schedule for discovered sources (default 1440)

You can create a `.env` file (see `backend/.env.example`), it will be auto-loaded.

## API

- `GET /health`
- `GET /api/businesses` (requires API key if configured)
- `POST /api/businesses`
- `GET /api/tours?location=Adelaide&min_date=2025-09-01&max_price=100`
- `POST /api/tours`
- `GET /ui` – minimal onboarding form to add sources
- `POST /api/sources` – programmatic source creation
- `POST /api/discovery/run` – manual discovery trigger (requires `SERPAPI_KEY` configured)

OpenAPI docs at `/docs`.

## Scraper worker

Skeleton under `backend/app/worker/`. A scheduler runs periodically and:
- Processes active `ScrapeSource` entries
- Optionally runs discovery for AU tour companies if `SERPAPI_KEY` is set

Run locally:

```bash
python -m app.worker.scheduler --module app.worker.scheduler --app-dir backend
```

Or from `backend` directory:

```bash
cd backend
python -m app.worker.scheduler
```

## Seeding sample data

From repository root:

```bash
python backend/app/seed.py
```

Or from `backend` directory (after activating venv):

```bash
python -m app.seed
```

## Next steps

- Integrate Scrapy/Playwright for robust scraping
- Add OAuth/tenant onboarding, rate limits, and API keys per customer
- Add pagination, sorting, and GraphQL
- Add Alembic migrations
