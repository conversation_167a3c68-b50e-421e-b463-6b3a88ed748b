<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Manage Tour Sources</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            max-width: 1200px;
            margin: 0 auto;
            padding: 20px;
            background-color: #f5f5f5;
        }
        .header {
            background: white;
            padding: 20px;
            border-radius: 8px;
            margin-bottom: 20px;
            box-shadow: 0 2px 4px rgba(0,0,0,0.1);
        }
        .nav {
            margin-bottom: 20px;
        }
        .nav a {
            display: inline-block;
            padding: 10px 20px;
            background: #007bff;
            color: white;
            text-decoration: none;
            border-radius: 4px;
            margin-right: 10px;
        }
        .nav a:hover {
            background: #0056b3;
        }
        .source-card {
            background: white;
            border-radius: 8px;
            padding: 20px;
            margin-bottom: 15px;
            box-shadow: 0 2px 4px rgba(0,0,0,0.1);
            border-left: 4px solid #28a745;
        }
        .source-card.inactive {
            border-left-color: #dc3545;
            opacity: 0.7;
        }
        .source-header {
            display: flex;
            justify-content: space-between;
            align-items: center;
            margin-bottom: 10px;
        }
        .source-title {
            font-size: 18px;
            font-weight: bold;
            color: #333;
        }
        .source-status {
            padding: 4px 12px;
            border-radius: 20px;
            font-size: 12px;
            font-weight: bold;
            text-transform: uppercase;
        }
        .status-active {
            background: #d4edda;
            color: #155724;
        }
        .status-inactive {
            background: #f8d7da;
            color: #721c24;
        }
        .source-url {
            color: #666;
            margin-bottom: 10px;
            word-break: break-all;
        }
        .source-meta {
            display: flex;
            flex-wrap: wrap;
            gap: 15px;
            margin-bottom: 15px;
            font-size: 14px;
            color: #666;
        }
        .actions {
            display: flex;
            gap: 10px;
            flex-wrap: wrap;
        }
        .btn {
            padding: 8px 16px;
            border: none;
            border-radius: 4px;
            cursor: pointer;
            font-size: 14px;
            text-decoration: none;
            display: inline-block;
        }
        .btn-primary { background: #007bff; color: white; }
        .btn-success { background: #28a745; color: white; }
        .btn-warning { background: #ffc107; color: #212529; }
        .btn-danger { background: #dc3545; color: white; }
        .btn-info { background: #17a2b8; color: white; }
        .btn:hover { opacity: 0.8; }
        .edit-form {
            display: none;
            margin-top: 15px;
            padding: 15px;
            background: #f8f9fa;
            border-radius: 4px;
        }
        .edit-form.show { display: block; }
        .form-group {
            margin-bottom: 15px;
        }
        .form-group label {
            display: block;
            margin-bottom: 5px;
            font-weight: bold;
        }
        .form-group input, .form-group select {
            width: 100%;
            padding: 8px;
            border: 1px solid #ddd;
            border-radius: 4px;
            font-size: 14px;
        }
        .stats {
            background: white;
            padding: 20px;
            border-radius: 8px;
            margin-bottom: 20px;
            box-shadow: 0 2px 4px rgba(0,0,0,0.1);
        }
        .stats-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
            gap: 20px;
        }
        .stat-item {
            text-align: center;
        }
        .stat-number {
            font-size: 24px;
            font-weight: bold;
            color: #007bff;
        }
        .stat-label {
            color: #666;
            font-size: 14px;
        }
        .discovery-section {
            background: white;
            padding: 20px;
            border-radius: 8px;
            margin-bottom: 20px;
            box-shadow: 0 2px 4px rgba(0,0,0,0.1);
            text-align: center;
        }
        .discovery-section h3 {
            margin-top: 0;
            color: #333;
        }
    </style>
</head>
<body>
    <div class="header">
        <h1>Manage Tour Sources</h1>
        <div class="nav">
            <a href="/ui">Add New Source</a>
            <a href="/ui/manage">Manage Sources</a>
            <a href="/ui/businesses">View Businesses</a>
            <a href="/api/sources">API View</a>
        </div>
    </div>

    <div class="stats">
        <div class="stats-grid">
            <div class="stat-item">
                <div class="stat-number">{{ sources|selectattr("is_active")|list|length }}</div>
                <div class="stat-label">Active Sources</div>
            </div>
            <div class="stat-item">
                <div class="stat-number">{{ sources|rejectattr("is_active")|list|length }}</div>
                <div class="stat-label">Inactive Sources</div>
            </div>
            <div class="stat-item">
                <div class="stat-number">{{ sources|length }}</div>
                <div class="stat-label">Total Sources</div>
            </div>
            <div class="stat-item">
                <div class="stat-number">{{ sources|selectattr("last_run_at")|list|length }}</div>
                <div class="stat-label">Ever Processed</div>
            </div>
        </div>
    </div>

    <div class="discovery-section">
        <h3>Discovery & Actions</h3>
        <p>Find new tour companies or trigger immediate scraping for existing sources.</p>
        <p style="font-size: 14px; color: #666; margin-bottom: 15px;">
            <em>Sources are sorted with active sources first, then alphabetically by business name.</em>
        </p>
        <form method="post" action="/ui/manage/run-discovery" style="display: inline; margin-right: 10px;">
            <button type="submit" class="btn btn-info" onclick="return confirm('This will search for new Australian tour companies. Continue?')">
                🔍 Run Discovery
            </button>
        </form>

        <form method="post" action="/ui/manage/scrape-all" style="display: inline; margin-right: 10px;">
            <button type="submit" class="btn btn-success" onclick="return confirm('This will scrape all active sources and update tours. This may take several minutes. Continue?')">
                🚀 Scrape All Active Sources
            </button>
        </form>

        <a href="/ui/changes" class="btn btn-secondary" style="display: inline-block; padding: 10px 20px; background: #6c757d; color: white; text-decoration: none; border-radius: 4px;">
            📊 View Changes History
        </a>
    </div>

    {% for source in sources %}
    <div class="source-card {% if not source.is_active %}inactive{% endif %}">
        <div class="source-header">
            <div class="source-title">{{ source.business_name }}</div>
            <div class="source-status {% if source.is_active %}status-active{% else %}status-inactive{% endif %}">
                {% if source.is_active %}Active{% else %}Inactive{% endif %}
            </div>
        </div>
        
        <div class="source-url">{{ source.start_url }}</div>
        
        <div class="source-meta">
            <span><strong>ID:</strong> {{ source.id }}</span>
            <span><strong>Business ID:</strong> {{ source.business_id or 'Not linked' }}</span>
            <span><strong>Schedule:</strong> {{ source.schedule_minutes }} minutes</span>
            <span><strong>Created:</strong> {{ source.created_at.strftime('%Y-%m-%d %H:%M') if source.created_at else 'N/A' }}</span>
            <span><strong>Last Run:</strong> {{ source.last_run_at.strftime('%Y-%m-%d %H:%M') if source.last_run_at else 'Never' }}</span>
        </div>
        
        <div class="actions">
            <button class="btn btn-primary" onclick="toggleEdit({{ source.id }})">Edit</button>

            <form method="post" action="/ui/manage/{{ source.id }}/scrape-now" style="display: inline;">
                <button type="submit" class="btn btn-info" onclick="return confirm('This will immediately scrape {{ source.start_url }}. Continue?')">
                    🚀 Scrape Now
                </button>
            </form>

            <form method="post" action="/ui/manage/{{ source.id }}/toggle" style="display: inline;">
                <button type="submit" class="btn {% if source.is_active %}btn-warning{% else %}btn-success{% endif %}">
                    {% if source.is_active %}Deactivate{% else %}Activate{% endif %}
                </button>
            </form>

            <form method="post" action="/ui/manage/{{ source.id }}/delete" style="display: inline;"
                  onsubmit="return confirm('Are you sure you want to delete this source? This will also delete all associated tour data.')">
                <button type="submit" class="btn btn-danger">Delete</button>
            </form>
        </div>
        
        <div id="edit-{{ source.id }}" class="edit-form">
            <form method="post" action="/ui/manage/{{ source.id }}/edit">
                <div class="form-group">
                    <label for="business_name_{{ source.id }}">Business Name:</label>
                    <input type="text" id="business_name_{{ source.id }}" name="business_name" value="{{ source.business_name }}" required>
                </div>
                
                <div class="form-group">
                    <label for="start_url_{{ source.id }}">URL:</label>
                    <input type="url" id="start_url_{{ source.id }}" name="start_url" value="{{ source.start_url }}" required>
                </div>
                
                <div class="form-group">
                    <label for="schedule_minutes_{{ source.id }}">Schedule (minutes):</label>
                    <select id="schedule_minutes_{{ source.id }}" name="schedule_minutes">
                        <option value="5" {% if source.schedule_minutes == 5 %}selected{% endif %}>5 minutes</option>
                        <option value="60" {% if source.schedule_minutes == 60 %}selected{% endif %}>1 hour</option>
                        <option value="360" {% if source.schedule_minutes == 360 %}selected{% endif %}>6 hours</option>
                        <option value="720" {% if source.schedule_minutes == 720 %}selected{% endif %}>12 hours</option>
                        <option value="1440" {% if source.schedule_minutes == 1440 %}selected{% endif %}>24 hours</option>
                    </select>
                </div>
                
                <div class="actions">
                    <button type="submit" class="btn btn-success">Save Changes</button>
                    <button type="button" class="btn btn-warning" onclick="toggleEdit({{ source.id }})">Cancel</button>
                </div>
            </form>
        </div>
    </div>
    {% endfor %}

    {% if not sources %}
    <div class="source-card">
        <div class="source-title">No sources found</div>
        <p>No tour sources have been added yet. <a href="/ui">Add your first source</a>.</p>
    </div>
    {% endif %}

    <script>
        function toggleEdit(sourceId) {
            const editForm = document.getElementById('edit-' + sourceId);
            editForm.classList.toggle('show');
        }
    </script>
</body>
</html>
