<?php
/**
 * Simple Tours Widget - Add to functions.php
 */

// Simple function to display tours anywhere
function display_tours_simple($api_url = 'http://localhost:8000/api/tours/export/directory', $limit = 10) {
    $response = wp_remote_get($api_url);
    
    if (is_wp_error($response)) {
        return '<p>Unable to load tours at this time.</p>';
    }
    
    $data = json_decode(wp_remote_retrieve_body($response), true);
    
    if (!$data || empty($data['tours'])) {
        return '<p>No tours available.</p>';
    }
    
    $tours = array_slice($data['tours'], 0, $limit);
    
    ob_start();
    ?>
    <div class="simple-tours-widget">
        <h3>Featured Tours</h3>
        <div class="tours-list">
            <?php foreach ($tours as $tour): ?>
                <div class="tour-item">
                    <?php if (!empty($tour['image_url'])): ?>
                        <img src="<?php echo esc_url($tour['image_url']); ?>" 
                             alt="<?php echo esc_attr($tour['title']); ?>"
                             class="tour-thumb">
                    <?php endif; ?>
                    
                    <div class="tour-info">
                        <h4>
                            <?php if (!empty($tour['tour_url'])): ?>
                                <a href="<?php echo esc_url($tour['tour_url']); ?>" target="_blank">
                                    <?php echo esc_html($tour['title']); ?>
                                </a>
                            <?php else: ?>
                                <?php echo esc_html($tour['title']); ?>
                            <?php endif; ?>
                        </h4>
                        
                        <p class="tour-meta">
                            <?php if (!empty($tour['location'])): ?>
                                <span>📍 <?php echo esc_html($tour['location']); ?></span>
                            <?php endif; ?>
                            
                            <?php if (!empty($tour['price'])): ?>
                                <span>💰 From $<?php echo number_format($tour['price'], 0); ?></span>
                            <?php endif; ?>
                        </p>
                        
                        <p class="tour-business">
                            by <?php echo esc_html($tour['business_name']); ?>
                        </p>
                    </div>
                </div>
            <?php endforeach; ?>
        </div>
        
        <p class="tours-footer">
            <small>Last updated: <?php echo date('M j, Y', strtotime($data['last_export'])); ?></small>
        </p>
    </div>
    
    <style>
    .simple-tours-widget {
        background: #f9f9f9;
        padding: 20px;
        border-radius: 8px;
        margin: 20px 0;
    }
    
    .tours-list {
        margin: 15px 0;
    }
    
    .tour-item {
        display: flex;
        gap: 15px;
        padding: 15px 0;
        border-bottom: 1px solid #ddd;
    }
    
    .tour-item:last-child {
        border-bottom: none;
    }
    
    .tour-thumb {
        width: 80px;
        height: 60px;
        object-fit: cover;
        border-radius: 4px;
        flex-shrink: 0;
    }
    
    .tour-info h4 {
        margin: 0 0 8px 0;
        font-size: 16px;
    }
    
    .tour-info h4 a {
        color: #333;
        text-decoration: none;
    }
    
    .tour-info h4 a:hover {
        color: #007cba;
    }
    
    .tour-meta {
        margin: 5px 0;
        font-size: 14px;
        color: #666;
    }
    
    .tour-meta span {
        margin-right: 15px;
    }
    
    .tour-business {
        margin: 5px 0 0 0;
        font-size: 13px;
        color: #999;
    }
    
    .tours-footer {
        text-align: center;
        margin-top: 15px;
        padding-top: 15px;
        border-top: 1px solid #ddd;
    }
    </style>
    <?php
    
    return ob_get_clean();
}

// Create a widget
class Tours_Widget extends WP_Widget {
    
    function __construct() {
        parent::__construct(
            'tours_widget',
            'Tours Directory Widget',
            array('description' => 'Display tours from your directory API')
        );
    }
    
    public function widget($args, $instance) {
        echo $args['before_widget'];
        
        if (!empty($instance['title'])) {
            echo $args['before_title'] . apply_filters('widget_title', $instance['title']) . $args['after_title'];
        }
        
        $api_url = !empty($instance['api_url']) ? $instance['api_url'] : 'http://localhost:8000/api/tours/export/directory';
        $limit = !empty($instance['limit']) ? intval($instance['limit']) : 5;
        
        echo display_tours_simple($api_url, $limit);
        
        echo $args['after_widget'];
    }
    
    public function form($instance) {
        $title = !empty($instance['title']) ? $instance['title'] : 'Featured Tours';
        $api_url = !empty($instance['api_url']) ? $instance['api_url'] : 'http://localhost:8000/api/tours/export/directory';
        $limit = !empty($instance['limit']) ? $instance['limit'] : 5;
        ?>
        <p>
            <label for="<?php echo $this->get_field_id('title'); ?>">Title:</label>
            <input class="widefat" id="<?php echo $this->get_field_id('title'); ?>" 
                   name="<?php echo $this->get_field_name('title'); ?>" type="text" 
                   value="<?php echo esc_attr($title); ?>">
        </p>
        <p>
            <label for="<?php echo $this->get_field_id('api_url'); ?>">API URL:</label>
            <input class="widefat" id="<?php echo $this->get_field_id('api_url'); ?>" 
                   name="<?php echo $this->get_field_name('api_url'); ?>" type="url" 
                   value="<?php echo esc_attr($api_url); ?>">
        </p>
        <p>
            <label for="<?php echo $this->get_field_id('limit'); ?>">Number of tours:</label>
            <input class="tiny-text" id="<?php echo $this->get_field_id('limit'); ?>" 
                   name="<?php echo $this->get_field_name('limit'); ?>" type="number" 
                   value="<?php echo esc_attr($limit); ?>" min="1" max="50">
        </p>
        <?php
    }
    
    public function update($new_instance, $old_instance) {
        $instance = array();
        $instance['title'] = (!empty($new_instance['title'])) ? sanitize_text_field($new_instance['title']) : '';
        $instance['api_url'] = (!empty($new_instance['api_url'])) ? esc_url_raw($new_instance['api_url']) : '';
        $instance['limit'] = (!empty($new_instance['limit'])) ? intval($new_instance['limit']) : 5;
        return $instance;
    }
}

// Register the widget
add_action('widgets_init', function() {
    register_widget('Tours_Widget');
});

// Add shortcode for simple display
add_shortcode('simple_tours', function($atts) {
    $atts = shortcode_atts(array(
        'api_url' => 'http://localhost:8000/api/tours/export/directory',
        'limit' => 10
    ), $atts);
    
    return display_tours_simple($atts['api_url'], intval($atts['limit']));
});
?>
