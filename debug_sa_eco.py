#!/usr/bin/env python3
"""
Debug script to analyze SA Eco Tours page structure
"""
import asyncio
import sys
import re

sys.path.insert(0, '/app')

from app.worker.scraper import fetch_html
from bs4 import BeautifulSoup

async def debug_sa_eco():
    """Debug SA Eco Tours page to find all tour elements"""
    url = "https://saecotours.com.au/tours"
    
    print(f"Debugging SA Eco Tours: {url}")
    print("=" * 60)
    
    try:
        html = await fetch_html(url)
        soup = BeautifulSoup(html, 'html.parser')
        
        # Look for all links that might be tours
        print("1. ANALYZING ALL LINKS:")
        links = soup.find_all('a', href=True)
        tour_links = []
        
        for link in links:
            text = link.get_text(strip=True)
            href = link.get('href', '')
            
            # Look for tour-related text
            tour_keywords = ['tour', 'day', 'experience', 'adventure', 'trip', 'excursion', 'package', 'activity']
            if any(kw in text.lower() for kw in tour_keywords) and len(text) > 5 and len(text) < 200:
                # Skip navigation/menu items
                skip_keywords = ['menu', 'navigation', 'footer', 'header', 'contact', 'about', 'home']
                if not any(skip in text.lower() for skip in skip_keywords):
                    tour_links.append((text, href))
        
        print(f"   Found {len(tour_links)} potential tour links:")
        for i, (text, href) in enumerate(tour_links[:15], 1):  # Show first 15
            print(f"   {i:2d}. {text}")
            print(f"       -> {href}")
            print()
        
        # Look for specific patterns that might contain multiple tours
        print("\n2. ANALYZING CONTAINER PATTERNS:")
        
        patterns_to_test = [
            '.elementor-widget-container',
            '.elementor-element',
            '.jet-listing-grid__item',
            '.elementor-posts-container',
            '.elementor-post',
            '[class*="tour"]',
            '[class*="post"]',
            'article',
            '.entry',
            '.product'
        ]
        
        for pattern in patterns_to_test:
            elements = soup.select(pattern)
            if elements:
                print(f"   Pattern '{pattern}': {len(elements)} elements")
                
                # Check if any contain tour-related text
                tour_containing = 0
                for elem in elements:
                    text = elem.get_text(strip=True)
                    if any(kw in text.lower() for kw in tour_keywords) and len(text) > 20:
                        tour_containing += 1
                
                if tour_containing > 0:
                    print(f"     -> {tour_containing} elements contain tour keywords")
                    
                    # Show first few examples
                    for i, elem in enumerate(elements[:3]):
                        text = elem.get_text(strip=True)
                        if any(kw in text.lower() for kw in tour_keywords):
                            print(f"     Example {i+1}: {text[:100]}...")
                print()
        
        # Look for specific SA Eco Tours patterns
        print("\n3. LOOKING FOR SA ECO TOURS SPECIFIC PATTERNS:")
        
        # Check for tour titles in the page
        all_text = soup.get_text()
        
        # Look for multi-day tour patterns
        day_patterns = [
            r'(\d+[-\s]*day[s]?\s+[^.]{10,100})',
            r'([^.]{10,100}\s+\d+[-\s]*day[s]?)',
        ]
        
        found_day_tours = []
        for pattern in day_patterns:
            matches = re.findall(pattern, all_text, re.IGNORECASE)
            for match in matches:
                clean_match = re.sub(r'\s+', ' ', match.strip())
                if len(clean_match) > 15 and len(clean_match) < 150:
                    found_day_tours.append(clean_match)
        
        # Remove duplicates
        unique_day_tours = list(set(found_day_tours))
        
        print(f"   Found {len(unique_day_tours)} potential day tours in text:")
        for i, tour in enumerate(unique_day_tours[:10], 1):
            print(f"   {i:2d}. {tour}")
        
    except Exception as e:
        print(f"Error: {e}")
        import traceback
        traceback.print_exc()

if __name__ == "__main__":
    asyncio.run(debug_sa_eco())
