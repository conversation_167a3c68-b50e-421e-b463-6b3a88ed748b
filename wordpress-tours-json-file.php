<?php
/**
 * WordPress Tours Display from JSON File
 * Upload your tours.json file to WordPress Media Library and use the URL
 */

// Add shortcode to display tours from JSON file
add_shortcode('tours_from_json', 'display_tours_from_json');

function display_tours_from_json($atts) {
    // Parse shortcode attributes
    $atts = shortcode_atts(array(
        'json_url' => '', // URL to uploaded JSON file
        'json_file' => '', // Alternative: filename in uploads directory
        'location' => '',
        'max_price' => '',
        'limit' => 50,
        'show_images' => 'true',
        'columns' => 3
    ), $atts);

    // Determine JSON source
    $json_url = '';
    if (!empty($atts['json_url'])) {
        $json_url = $atts['json_url'];
    } elseif (!empty($atts['json_file'])) {
        $upload_dir = wp_upload_dir();
        $json_url = $upload_dir['baseurl'] . '/' . ltrim($atts['json_file'], '/');
    } else {
        // Default: look for tours.json in uploads directory
        $upload_dir = wp_upload_dir();
        $json_url = $upload_dir['baseurl'] . '/tours.json';
    }

    // Fetch tours data from JSON file
    $tours_data = fetch_tours_from_json($json_url);
    
    if (!$tours_data || empty($tours_data['tours'])) {
        return '<div class="tours-error">No tours data found. Please upload a valid tours.json file.</div>';
    }

    $tours = $tours_data['tours'];
    
    // Filter tours based on attributes
    if (!empty($atts['location'])) {
        $tours = array_filter($tours, function($tour) use ($atts) {
            return stripos($tour['location'] ?? '', $atts['location']) !== false;
        });
    }
    
    if (!empty($atts['max_price'])) {
        $tours = array_filter($tours, function($tour) use ($atts) {
            return isset($tour['price']) && $tour['price'] <= floatval($atts['max_price']);
        });
    }
    
    // Limit results
    $tours = array_slice($tours, 0, intval($atts['limit']));
    
    // Generate HTML
    ob_start();
    ?>
    <div class="tours-directory">
        <div class="tours-header">
            <h3>Australian Tours Directory</h3>
            <p class="tours-count"><?php echo count($tours); ?> tours available</p>
            <p class="tours-updated">Last updated: <?php echo date('F j, Y g:i A', strtotime($tours_data['last_export'])); ?></p>
        </div>
        
        <div class="tours-filters">
            <input type="text" id="tour-search" placeholder="Search tours..." onkeyup="filterTours()">
            <select id="location-filter" onchange="filterTours()">
                <option value="">All Locations</option>
                <?php
                $locations = array_unique(array_filter(array_column($tours, 'location')));
                sort($locations);
                foreach ($locations as $location): ?>
                    <option value="<?php echo esc_attr($location); ?>"><?php echo esc_html($location); ?></option>
                <?php endforeach; ?>
            </select>
        </div>
        
        <div class="tours-grid tours-columns-<?php echo esc_attr($atts['columns']); ?>">
            <?php foreach ($tours as $index => $tour): ?>
                <div class="tour-card" data-location="<?php echo esc_attr($tour['location'] ?? ''); ?>" data-title="<?php echo esc_attr(strtolower($tour['title'])); ?>">
                    <?php if ($atts['show_images'] === 'true' && !empty($tour['image_url'])): ?>
                        <div class="tour-image">
                            <img src="<?php echo esc_url($tour['image_url']); ?>" 
                                 alt="<?php echo esc_attr($tour['title']); ?>"
                                 loading="lazy"
                                 onerror="this.style.display='none'">
                        </div>
                    <?php endif; ?>
                    
                    <div class="tour-content">
                        <h4 class="tour-title">
                            <?php if (!empty($tour['tour_url'])): ?>
                                <a href="<?php echo esc_url($tour['tour_url']); ?>" target="_blank" rel="noopener">
                                    <?php echo esc_html($tour['title']); ?>
                                </a>
                            <?php else: ?>
                                <?php echo esc_html($tour['title']); ?>
                            <?php endif; ?>
                        </h4>
                        
                        <?php if (!empty($tour['location'])): ?>
                            <p class="tour-location">
                                <span class="tour-icon">📍</span>
                                <?php echo esc_html($tour['location']); ?>
                            </p>
                        <?php endif; ?>
                        
                        <?php if (!empty($tour['price'])): ?>
                            <p class="tour-price">
                                <span class="tour-icon">💰</span>
                                From $<?php echo number_format($tour['price'], 0); ?>
                            </p>
                        <?php endif; ?>
                        
                        <div class="tour-business">
                            <p class="business-name">
                                <span class="tour-icon">🏢</span>
                                <?php if (!empty($tour['business_website'])): ?>
                                    <a href="<?php echo esc_url($tour['business_website']); ?>" target="_blank" rel="noopener">
                                        <?php echo esc_html($tour['business_name']); ?>
                                    </a>
                                <?php else: ?>
                                    <?php echo esc_html($tour['business_name']); ?>
                                <?php endif; ?>
                            </p>
                        </div>
                        
                        <?php if (!empty($tour['tour_url'])): ?>
                            <div class="tour-actions">
                                <a href="<?php echo esc_url($tour['tour_url']); ?>" 
                                   class="tour-button" 
                                   target="_blank" 
                                   rel="noopener">
                                    View Tour Details
                                </a>
                            </div>
                        <?php endif; ?>
                    </div>
                </div>
            <?php endforeach; ?>
        </div>
        
        <?php if (empty($tours)): ?>
            <div class="tours-empty">
                <p>No tours match your criteria. Try adjusting your filters.</p>
            </div>
        <?php endif; ?>
    </div>
    
    <script>
    function filterTours() {
        const searchTerm = document.getElementById('tour-search').value.toLowerCase();
        const locationFilter = document.getElementById('location-filter').value;
        const tourCards = document.querySelectorAll('.tour-card');
        
        tourCards.forEach(card => {
            const title = card.getAttribute('data-title');
            const location = card.getAttribute('data-location');
            
            const matchesSearch = title.includes(searchTerm);
            const matchesLocation = !locationFilter || location === locationFilter;
            
            if (matchesSearch && matchesLocation) {
                card.style.display = 'block';
            } else {
                card.style.display = 'none';
            }
        });
    }
    </script>
    
    <style>
    .tours-directory {
        max-width: 1200px;
        margin: 0 auto;
        padding: 20px;
    }
    
    .tours-header {
        text-align: center;
        margin-bottom: 30px;
        padding-bottom: 20px;
        border-bottom: 2px solid #eee;
    }
    
    .tours-filters {
        display: flex;
        gap: 15px;
        margin-bottom: 30px;
        justify-content: center;
        flex-wrap: wrap;
    }
    
    .tours-filters input,
    .tours-filters select {
        padding: 10px 15px;
        border: 1px solid #ddd;
        border-radius: 6px;
        font-size: 14px;
    }
    
    .tours-filters input {
        min-width: 250px;
    }
    
    .tours-grid {
        display: grid;
        gap: 25px;
        margin-bottom: 30px;
    }
    
    .tours-columns-1 { grid-template-columns: 1fr; }
    .tours-columns-2 { grid-template-columns: repeat(2, 1fr); }
    .tours-columns-3 { grid-template-columns: repeat(3, 1fr); }
    .tours-columns-4 { grid-template-columns: repeat(4, 1fr); }
    
    @media (max-width: 768px) {
        .tours-grid {
            grid-template-columns: 1fr !important;
        }
        .tours-filters {
            flex-direction: column;
            align-items: center;
        }
        .tours-filters input {
            min-width: auto;
            width: 100%;
            max-width: 300px;
        }
    }
    
    .tour-card {
        background: white;
        border-radius: 12px;
        box-shadow: 0 4px 6px rgba(0,0,0,0.1);
        overflow: hidden;
        transition: transform 0.3s ease, box-shadow 0.3s ease;
    }
    
    .tour-card:hover {
        transform: translateY(-5px);
        box-shadow: 0 8px 25px rgba(0,0,0,0.15);
    }
    
    .tour-image {
        width: 100%;
        height: 200px;
        overflow: hidden;
        background: #f5f5f5;
    }
    
    .tour-image img {
        width: 100%;
        height: 100%;
        object-fit: cover;
        transition: transform 0.3s ease;
    }
    
    .tour-card:hover .tour-image img {
        transform: scale(1.05);
    }
    
    .tour-content {
        padding: 20px;
    }
    
    .tour-title {
        margin: 0 0 15px 0;
        font-size: 18px;
        line-height: 1.4;
    }
    
    .tour-title a {
        color: #333;
        text-decoration: none;
        transition: color 0.3s ease;
    }
    
    .tour-title a:hover {
        color: #007cba;
    }
    
    .tour-location,
    .tour-price,
    .business-name {
        margin: 8px 0;
        font-size: 14px;
        color: #666;
        display: flex;
        align-items: center;
        gap: 8px;
    }
    
    .tour-icon {
        font-size: 16px;
    }
    
    .tour-price {
        font-weight: bold;
        color: #2c5aa0;
    }
    
    .business-name a {
        color: #666;
        text-decoration: none;
    }
    
    .business-name a:hover {
        color: #007cba;
    }
    
    .tour-actions {
        margin-top: 15px;
        padding-top: 15px;
        border-top: 1px solid #eee;
    }
    
    .tour-button {
        display: inline-block;
        background: #007cba;
        color: white;
        padding: 10px 20px;
        border-radius: 6px;
        text-decoration: none;
        font-size: 14px;
        font-weight: bold;
        transition: background 0.3s ease;
    }
    
    .tour-button:hover {
        background: #005a87;
        color: white;
    }
    
    .tours-error,
    .tours-empty {
        text-align: center;
        padding: 40px 20px;
        background: #f9f9f9;
        border-radius: 8px;
        color: #666;
    }
    </style>
    <?php
    
    return ob_get_clean();
}

function fetch_tours_from_json($json_url) {
    // Use WordPress HTTP API to fetch JSON
    $response = wp_remote_get($json_url, array(
        'timeout' => 30,
        'headers' => array(
            'Accept' => 'application/json',
        )
    ));
    
    if (is_wp_error($response)) {
        error_log('Tours JSON Error: ' . $response->get_error_message());
        return false;
    }
    
    $body = wp_remote_retrieve_body($response);
    $data = json_decode($body, true);
    
    if (json_last_error() !== JSON_ERROR_NONE) {
        error_log('Tours JSON Parse Error: ' . json_last_error_msg());
        return false;
    }
    
    return $data;
}

// Add admin page for JSON file management
add_action('admin_menu', 'tours_json_admin_menu');

function tours_json_admin_menu() {
    add_options_page(
        'Tours JSON Settings',
        'Tours JSON',
        'manage_options',
        'tours-json',
        'tours_json_admin_page'
    );
}

function tours_json_admin_page() {
    if (isset($_POST['submit'])) {
        update_option('tours_json_url', esc_url_raw($_POST['json_url']));
        echo '<div class="notice notice-success"><p>Settings saved!</p></div>';
    }
    
    $json_url = get_option('tours_json_url', '');
    $upload_dir = wp_upload_dir();
    ?>
    <div class="wrap">
        <h1>Tours JSON File Settings</h1>
        
        <h2>Step 1: Upload JSON File</h2>
        <p>Go to <a href="<?php echo admin_url('media-new.php'); ?>">Media > Add New</a> and upload your <code>tours.json</code> file.</p>
        
        <h2>Step 2: Set JSON File URL</h2>
        <form method="post">
            <table class="form-table">
                <tr>
                    <th scope="row">JSON File URL</th>
                    <td>
                        <input type="url" name="json_url" value="<?php echo esc_attr($json_url); ?>" class="regular-text" />
                        <p class="description">
                            URL to your uploaded tours.json file<br>
                            Example: <code><?php echo $upload_dir['baseurl']; ?>/tours.json</code>
                        </p>
                    </td>
                </tr>
            </table>
            <?php submit_button(); ?>
        </form>
        
        <h2>Usage Examples</h2>
        <p><strong>Basic usage:</strong></p>
        <code>[tours_from_json]</code>
        
        <p><strong>With custom JSON file:</strong></p>
        <code>[tours_from_json json_url="<?php echo $upload_dir['baseurl']; ?>/my-tours.json"]</code>
        
        <p><strong>With filters:</strong></p>
        <code>[tours_from_json location="Adelaide" max_price="500" limit="20"]</code>
        
        <h3>Available Parameters:</h3>
        <ul>
            <li><code>json_url</code> - Direct URL to JSON file</li>
            <li><code>json_file</code> - Filename in uploads directory (e.g., "tours.json")</li>
            <li><code>location</code> - Filter by location</li>
            <li><code>max_price</code> - Maximum price filter</li>
            <li><code>limit</code> - Number of tours to show (default: 50)</li>
            <li><code>show_images</code> - Show tour images (true/false)</li>
            <li><code>columns</code> - Grid columns (1-4, default: 3)</li>
        </ul>
        
        <h2>How to Update Tours</h2>
        <ol>
            <li>Download fresh JSON from: <code>http://your-server:8000/api/tours/export/directory</code></li>
            <li>Upload the new file to WordPress Media Library</li>
            <li>Update the JSON URL above if needed</li>
            <li>Tours will automatically refresh on your pages</li>
        </ol>
    </div>
    <?php
}
?>
