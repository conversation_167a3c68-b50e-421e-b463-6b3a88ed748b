from fastapi import APIRouter, Depends, HTTPException
from sqlalchemy.orm import Session

from ..auth import require_api_key
from ..db import get_db
# Discovery functionality disabled in main app - use worker app instead


router = APIRouter(prefix="/api/discovery", tags=["discovery"], dependencies=[Depends(require_api_key)])


@router.post("/run")
async def run_discovery(db: Session = Depends(get_db)):
    raise HTTPException(status_code=501, detail="Discovery functionality is available in the worker app only")


