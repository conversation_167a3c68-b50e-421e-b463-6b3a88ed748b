<!DOCTYPE html>
<html>
<head>
    <title>Tours Directory from JSON</title>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <style>
        .tours-container {
            max-width: 1200px;
            margin: 0 auto;
            padding: 20px;
            font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
        }
        
        .tours-header {
            text-align: center;
            margin-bottom: 30px;
            padding-bottom: 20px;
            border-bottom: 2px solid #eee;
        }
        
        .tours-filters {
            display: flex;
            gap: 15px;
            margin-bottom: 30px;
            justify-content: center;
            flex-wrap: wrap;
        }
        
        .tours-filters input,
        .tours-filters select {
            padding: 10px 15px;
            border: 1px solid #ddd;
            border-radius: 6px;
            font-size: 14px;
        }
        
        .tours-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(350px, 1fr));
            gap: 25px;
            margin-bottom: 30px;
        }
        
        .tour-card {
            background: white;
            border-radius: 12px;
            box-shadow: 0 4px 6px rgba(0,0,0,0.1);
            overflow: hidden;
            transition: transform 0.3s ease, box-shadow 0.3s ease;
        }
        
        .tour-card:hover {
            transform: translateY(-5px);
            box-shadow: 0 8px 25px rgba(0,0,0,0.15);
        }
        
        .tour-image {
            width: 100%;
            height: 200px;
            overflow: hidden;
            background: #f5f5f5;
        }
        
        .tour-image img {
            width: 100%;
            height: 100%;
            object-fit: cover;
        }
        
        .tour-content {
            padding: 20px;
        }
        
        .tour-title {
            margin: 0 0 15px 0;
            font-size: 18px;
            line-height: 1.4;
        }
        
        .tour-title a {
            color: #333;
            text-decoration: none;
        }
        
        .tour-title a:hover {
            color: #007cba;
        }
        
        .tour-meta {
            margin: 8px 0;
            font-size: 14px;
            color: #666;
            display: flex;
            align-items: center;
            gap: 8px;
        }
        
        .tour-price {
            font-weight: bold;
            color: #2c5aa0;
        }
        
        .tour-button {
            display: inline-block;
            background: #007cba;
            color: white;
            padding: 10px 20px;
            border-radius: 6px;
            text-decoration: none;
            font-size: 14px;
            font-weight: bold;
            margin-top: 15px;
        }
        
        .tour-button:hover {
            background: #005a87;
        }
        
        .loading, .error {
            text-align: center;
            padding: 40px;
            color: #666;
        }
    </style>
</head>
<body>
    <div class="tours-container">
        <div class="tours-header">
            <h1>Australian Tours Directory</h1>
            <p id="tours-count">Loading tours...</p>
        </div>
        
        <div class="tours-filters" style="display: none;">
            <input type="text" id="search-input" placeholder="Search tours..." onkeyup="filterTours()">
            <select id="location-filter" onchange="filterTours()">
                <option value="">All Locations</option>
            </select>
            <input type="number" id="price-filter" placeholder="Max price" onchange="filterTours()">
        </div>
        
        <div id="tours-grid" class="tours-grid">
            <div class="loading">Loading tours...</div>
        </div>
    </div>

    <script>
        let allTours = [];
        let filteredTours = [];

        // Load tours from JSON file
        async function loadTours() {
            try {
                // Change this to your JSON file path
                const response = await fetch('tours.json');
                
                if (!response.ok) {
                    throw new Error(`HTTP ${response.status}: ${response.statusText}`);
                }
                
                const data = await response.json();
                allTours = data.tours || [];
                filteredTours = [...allTours];
                
                displayTours();
                setupFilters();
                
                document.getElementById('tours-count').textContent = 
                    `${allTours.length} tours available • Last updated: ${new Date(data.last_export).toLocaleDateString()}`;
                
                document.querySelector('.tours-filters').style.display = 'flex';
                
            } catch (error) {
                console.error('Error loading tours:', error);
                document.getElementById('tours-grid').innerHTML = `
                    <div class="error">
                        <h3>Unable to load tours</h3>
                        <p>Please make sure the tours.json file is uploaded and accessible.</p>
                        <p>Error: ${error.message}</p>
                    </div>
                `;
            }
        }

        function displayTours() {
            const grid = document.getElementById('tours-grid');
            
            if (filteredTours.length === 0) {
                grid.innerHTML = '<div class="error">No tours match your criteria.</div>';
                return;
            }
            
            grid.innerHTML = filteredTours.map(tour => `
                <div class="tour-card" data-location="${tour.location || ''}" data-title="${tour.title.toLowerCase()}">
                    ${tour.image_url ? `
                        <div class="tour-image">
                            <img src="${tour.image_url}" alt="${tour.title}" onerror="this.parentElement.style.display='none'">
                        </div>
                    ` : ''}
                    
                    <div class="tour-content">
                        <h3 class="tour-title">
                            ${tour.tour_url ? `
                                <a href="${tour.tour_url}" target="_blank" rel="noopener">
                                    ${tour.title}
                                </a>
                            ` : tour.title}
                        </h3>
                        
                        ${tour.location ? `
                            <div class="tour-meta">
                                <span>📍</span>
                                <span>${tour.location}</span>
                            </div>
                        ` : ''}
                        
                        ${tour.price ? `
                            <div class="tour-meta tour-price">
                                <span>💰</span>
                                <span>From $${tour.price.toLocaleString()}</span>
                            </div>
                        ` : ''}
                        
                        <div class="tour-meta">
                            <span>🏢</span>
                            <span>
                                ${tour.business_website ? `
                                    <a href="${tour.business_website}" target="_blank" rel="noopener">
                                        ${tour.business_name}
                                    </a>
                                ` : tour.business_name}
                            </span>
                        </div>
                        
                        ${tour.tour_url ? `
                            <a href="${tour.tour_url}" class="tour-button" target="_blank" rel="noopener">
                                View Tour Details
                            </a>
                        ` : ''}
                    </div>
                </div>
            `).join('');
        }

        function setupFilters() {
            // Populate location filter
            const locations = [...new Set(allTours.map(tour => tour.location).filter(Boolean))].sort();
            const locationFilter = document.getElementById('location-filter');
            
            locations.forEach(location => {
                const option = document.createElement('option');
                option.value = location;
                option.textContent = location;
                locationFilter.appendChild(option);
            });
        }

        function filterTours() {
            const searchTerm = document.getElementById('search-input').value.toLowerCase();
            const locationFilter = document.getElementById('location-filter').value;
            const priceFilter = parseFloat(document.getElementById('price-filter').value) || Infinity;
            
            filteredTours = allTours.filter(tour => {
                const matchesSearch = tour.title.toLowerCase().includes(searchTerm) ||
                                    (tour.business_name && tour.business_name.toLowerCase().includes(searchTerm));
                const matchesLocation = !locationFilter || tour.location === locationFilter;
                const matchesPrice = !tour.price || tour.price <= priceFilter;
                
                return matchesSearch && matchesLocation && matchesPrice;
            });
            
            displayTours();
            document.getElementById('tours-count').textContent = 
                `${filteredTours.length} of ${allTours.length} tours shown`;
        }

        // Load tours when page loads
        document.addEventListener('DOMContentLoaded', loadTours);
    </script>
</body>
</html>
