<!doctype html>
<html>
  <head>
    <meta charset="utf-8" />
    <meta name="viewport" content="width=device-width, initial-scale=1" />
    <title>Submit Scrape Source</title>
    <style>
      body { font-family: system-ui, -apple-system, Segoe UI, Roboto, Ubuntu, Cantarell, 'Noto Sans', 'Helvetica Neue', Arial, 'Apple Color Emoji', 'Segoe UI Emoji'; margin: 2rem; }
      form { display: grid; gap: .75rem; max-width: 560px; }
      input[type=text], input[type=url] { padding: .6rem .7rem; font-size: 1rem; }
      button { padding: .6rem 1rem; font-size: 1rem; cursor: pointer; }
      table { border-collapse: collapse; margin-top: 2rem; width: 100%; }
      th, td { border-bottom: 1px solid #e5e7eb; text-align: left; padding: .5rem .25rem; }
      .muted { color: #6b7280; font-size: .9rem; }
      .nav { margin-bottom: 2rem; }
      .nav a { display: inline-block; padding: .5rem 1rem; background: #007bff; color: white; text-decoration: none; border-radius: 4px; margin-right: .5rem; }
      .nav a:hover { background: #0056b3; }
    </style>
  </head>
  <body>
    <div class="nav">
      <a href="/ui">Add Source</a>
      <a href="/ui/manage">Manage Sources</a>
      <a href="/api/sources">API View</a>
    </div>
    <h1>Add a source</h1>
    <form method="post">
      <label>Business name</label>
      <input type="text" name="business_name" placeholder="Acme Tours" required />
      <label>Start URL</label>
      <input type="url" name="start_url" placeholder="https://example.com/tours" required />
      <button type="submit">Save</button>
      <div class="muted">Tip: this will be queued for automated scraping.</div>
    </form>

    <h2>Recent sources</h2>
    <table>
      <thead>
        <tr>
          <th>Business</th>
          <th>Business ID</th>
          <th>URL</th>
          <th>Active</th>
          <th>Last run</th>
        </tr>
      </thead>
      <tbody>
        {% for s in sources %}
        <tr>
          <td>{{ s.business_name }}</td>
          <td>{{ s.business_id or '-' }}</td>
          <td><a href="{{ s.start_url }}" target="_blank" rel="noopener">{{ s.start_url }}</a></td>
          <td>{{ 'yes' if s.is_active else 'no' }}</td>
          <td>{{ s.last_run_at or '-' }}</td>
        </tr>
        {% endfor %}
      </tbody>
    </table>
  </body>
  </html>


