from __future__ import annotations

import re
from dataclasses import dataclass
from datetime import datetime
from typing import Iterable, Optional

import httpx
from bs4 import BeautifulSoup  # type: ignore


@dataclass
class TourItem:
    title: str
    location: Optional[str]
    start_date: Optional[str]
    end_date: Optional[str]
    price: Optional[str]
    url: Optional[str]
    image_url: Optional[str]
    last_updated: datetime


async def fetch_html(url: str, timeout: float = 20.0) -> str:
    async with httpx.AsyncClient(timeout=timeout, follow_redirects=True) as client:
        resp = await client.get(url)
        resp.raise_for_status()
        return resp.text




def parse_example(html: str, base_url: str = "") -> Iterable[TourItem]:
    """Parse HTML for tour listings using realistic patterns for actual tour websites."""
    soup = BeautifulSoup(html, "html.parser")

    # Look for structured data (JSON-LD) first - many tour sites use this
    json_ld_scripts = soup.find_all('script', type='application/ld+json')
    for script in json_ld_scripts:
        try:
            import json
            data = json.loads(script.string)
            if isinstance(data, dict) and data.get('@type') in ['Product', 'TouristTrip', 'Event']:
                # Extract image from JSON-LD
                image_url = None
                if data.get('image'):
                    if isinstance(data['image'], str):
                        image_url = data['image']
                    elif isinstance(data['image'], dict):
                        image_url = data['image'].get('url')
                    elif isinstance(data['image'], list) and len(data['image']) > 0:
                        first_image = data['image'][0]
                        if isinstance(first_image, str):
                            image_url = first_image
                        elif isinstance(first_image, dict):
                            image_url = first_image.get('url')

                yield TourItem(
                    title=data.get('name', 'Tour'),
                    location=data.get('location', {}).get('name') if isinstance(data.get('location'), dict) else None,
                    start_date=None,
                    end_date=None,
                    price=str(data.get('offers', {}).get('price', '')) if data.get('offers') else None,
                    url=data.get('url'),
                    image_url=image_url,
                    last_updated=datetime.now(),
                )
        except (json.JSONDecodeError, AttributeError):
            continue

    # Look for tour links directly - simpler and more effective approach
    print(f"DEBUG: Analyzing page for tour links...")

    # Find all links that look like tours
    links = soup.find_all('a', href=True)
    tour_links = []

    for link in links:
        text = link.get_text(strip=True)
        href = link.get('href', '')

        # Must have tour-related keywords
        tour_keywords = ['tour', 'day', 'experience', 'adventure', 'trip', 'excursion', 'package', 'activity', 'safari', 'cruise']
        if not any(kw in text.lower() for kw in tour_keywords):
            continue

        # Must be reasonable length
        if len(text) < 10 or len(text) > 200:
            continue

        # Skip navigation/menu items and generic links
        skip_keywords = ['menu', 'navigation', 'footer', 'header', 'contact', 'about', 'home', 'destinations', 'durations', 'browse', 'view all', 'see all', '@', 'mailto']
        if any(skip in text.lower() for skip in skip_keywords):
            continue

        # Skip email addresses
        if '@' in text or href.startswith('mailto:'):
            continue

        # Skip if it's just a number or very generic
        if text.isdigit() or text.lower() in ['tour', 'tours', 'day', 'days']:
            continue

        # Filter out generic "all tours" listings
        text_lower = text.lower()
        generic_patterns = [
            r'\ball\s+tours?\b',
            r'\bview\s+all\b',
            r'\bsee\s+all\b',
            r'\bmore\s+tours?\b',
            r'\btours?\s+from\s+\w+$',  # "tours from sydney"
            r'^\w+\s+tours?$',  # Just "sydney tours"
        ]

        is_generic = False
        for pattern in generic_patterns:
            if re.search(pattern, text_lower):
                is_generic = True
                break

        if is_generic:
            continue

        tour_links.append((text, href))

    print(f"DEBUG: Found {len(tour_links)} potential tour links")

    # Process each tour link as a separate tour
    found_tours = []

    for text, href in tour_links:
        # Extract price from the link text or surrounding context
        price_text = text
        price = None

        # Enhanced price patterns including ranges
        price_patterns = [
            r'\$(\d+(?:,\d{3})*(?:\.\d{2})?)\s*-\s*\$(\d+(?:,\d{3})*(?:\.\d{2})?)',  # $100 - $200
            r'from\s*\$(\d+(?:,\d{3})*(?:\.\d{2})?)\s*to\s*\$(\d+(?:,\d{3})*(?:\.\d{2})?)',  # from $100 to $200
            r'from\s*\$(\d+(?:,\d{3})*(?:\.\d{2})?)',  # from $100
            r'\$(\d+(?:,\d{3})*(?:\.\d{2})?)',  # $100
            r'(\d+(?:,\d{3})*(?:\.\d{2})?)\s*AUD',  # 100 AUD
            r'Price:\s*\$(\d+(?:,\d{3})*(?:\.\d{2})?)',  # Price: $100
            r'AUD\s*\$?(\d+(?:,\d{3})*(?:\.\d{2})?)',  # AUD $100
        ]

        for pattern_re in price_patterns:
            match = re.search(pattern_re, price_text, re.IGNORECASE)
            if match:
                try:
                    if len(match.groups()) >= 2 and match.group(2):
                        # Price range - use the lower price
                        price_str = re.sub(r'[^\d.]', '', match.group(1))
                        price = float(price_str)
                    else:
                        # Single price
                        price_str = re.sub(r'[^\d.]', '', match.group(1))
                        price = float(price_str)
                except (ValueError, IndexError):
                    price = None
                break

        # Extract location from title - enhanced with more locations
        location = None
        text_lower = text.lower()

        # Comprehensive Australian locations (cities, regions, landmarks)
        location_keywords = [
            # Major cities
            'adelaide', 'sydney', 'melbourne', 'brisbane', 'perth', 'darwin',
            'canberra', 'hobart', 'cairns', 'townsville', 'gold coast', 'sunshine coast',
            'newcastle', 'wollongong', 'geelong', 'ballarat', 'bendigo', 'launceston',

            # States/territories
            'tasmania', 'queensland', 'nsw', 'new south wales', 'victoria',
            'south australia', 'western australia', 'northern territory', 'act',
            'australian capital territory',

            # Popular tourist regions/landmarks
            'blue mountains', 'hunter valley', 'barossa valley', 'margaret river',
            'great ocean road', 'grampians', 'flinders ranges', 'kangaroo island',
            'fraser island', 'whitsundays', 'daintree', 'kakadu', 'uluru', 'ayers rock',
            'cradle mountain', 'freycinet', 'port douglas', 'broome', 'exmouth',
            'monkey mia', 'rottnest island', 'phillip island', 'mornington peninsula',

            # Outback/remote areas
            'outback', 'coober pedy', 'alice springs', 'yorke peninsula', 'riverland',
            'murray river', 'lake eyre', 'arkaroola', 'wilpena pound', 'kimberley',
            'pilbara', 'nullarbor', 'eyre peninsula'
        ]

        # Look for location patterns like "from Sydney", "in Melbourne", "to Brisbane"
        location_patterns = [
            r'\bfrom\s+(\w+(?:\s+\w+)*)',
            r'\bin\s+(\w+(?:\s+\w+)*)',
            r'\bto\s+(\w+(?:\s+\w+)*)',
            r'\bat\s+(\w+(?:\s+\w+)*)',
            r'\b(\w+(?:\s+\w+)*)\s+tour',
            r'\b(\w+(?:\s+\w+)*)\s+day\s+trip',
        ]

        for pattern in location_patterns:
            matches = re.findall(pattern, text_lower)
            for match in matches:
                potential_location = match.strip()
                if potential_location in location_keywords:
                    location = potential_location.title()
                    break
            if location:
                break

        # Fallback: direct keyword matching
        if not location:
            for keyword in location_keywords:
                if keyword in text_lower:
                    location = keyword.title()
                    break

        # Make relative URLs absolute
        if href and href.startswith('/'):
            from urllib.parse import urljoin
            href = urljoin(base_url, href)

        # Enhanced image extraction - find the best image for this tour
        image_url = None

        # Strategy 1: Find the link element and look for associated images
        link_element = None
        for link in soup.find_all('a', href=True):
            link_href = link.get('href', '')
            if href and (link_href == href or href.endswith(link_href) or link_href.endswith(href.split('/')[-1])):
                link_element = link
                break

        if link_element:
            # Look for images in multiple places with priority order
            img_candidates = []

            # 1. Image directly inside the link
            img = link_element.find('img')
            if img and img.get('src'):
                img_candidates.append(img)

            # 2. Image in immediate parent container
            parent = link_element.parent
            if parent:
                parent_img = parent.find('img')
                if parent_img and parent_img.get('src'):
                    img_candidates.append(parent_img)

            # 3. Image in grandparent container (common in card layouts)
            if parent and parent.parent:
                grandparent_img = parent.parent.find('img')
                if grandparent_img and grandparent_img.get('src'):
                    img_candidates.append(grandparent_img)

            # 4. Look for images in sibling elements (common in grid layouts)
            if parent:
                for sibling in parent.find_all('img'):
                    if sibling.get('src'):
                        img_candidates.append(sibling)

            # Choose the best image (prefer larger images, avoid icons/logos/tracking)
            best_img = None
            for img in img_candidates:
                src = img.get('src', '')
                alt = img.get('alt', '').lower()

                # Enhanced filtering for unwanted images
                unwanted_patterns = [
                    # Social media tracking
                    'facebook.com/tr', 'google-analytics.com', 'googletagmanager.com',
                    'doubleclick.net', 'googlesyndication.com', 'googleadservices.com',
                    'twitter.com/i/adsct', 'linkedin.com/px', 'pinterest.com/v3/ct',
                    'snapchat.com/tr', 'tiktok.com/i18n/pixel',

                    # Analytics and tracking pixels
                    'hotjar.com', 'crazyegg.com', 'mouseflow.com', 'fullstory.com',
                    'amplitude.com', 'mixpanel.com', 'segment.com',

                    # Language selectors and flags
                    'lang', 'language', 'translate', 'translation', 'flag', 'flags',
                    'en.png', 'en.jpg', 'en.svg', 'au.png', 'au.jpg', 'au.svg',
                    'us.png', 'us.jpg', 'us.svg', 'uk.png', 'uk.jpg', 'uk.svg',
                    'de.png', 'fr.png', 'es.png', 'it.png', 'jp.png', 'cn.png',
                    'english', 'deutsch', 'francais', 'espanol', 'italiano',
                    'gtranslate', 'google-translate', 'weglot', 'transposh',

                    # Common unwanted image types
                    'icon', 'logo', 'avatar', 'thumb', 'pixel', 'tracking',
                    'badge', 'button', 'arrow', 'spinner', 'loader',

                    # File extensions that are likely not photos
                    '.svg', '.gif',

                    # Tiny images (1x1 pixels)
                    '1x1', 'spacer', 'blank',

                    # Social media icons
                    'facebook', 'twitter', 'instagram', 'youtube', 'linkedin',
                    'social', 'share'
                ]

                # Check if image source contains unwanted patterns
                if any(pattern in src.lower() for pattern in unwanted_patterns):
                    print(f"DEBUG: Skipping unwanted image: {src}")
                    continue

                # Check alt text for unwanted patterns
                if any(pattern in alt for pattern in ['icon', 'logo', 'avatar', 'social', 'share']):
                    print(f"DEBUG: Skipping image with unwanted alt: {alt}")
                    continue

                # Skip data URLs (base64 encoded)
                if src.startswith('data:'):
                    continue

                # Skip very small images (likely icons/pixels)
                width = img.get('width')
                height = img.get('height')
                if width and height:
                    try:
                        w, h = int(width), int(height)
                        if w < 50 or h < 50:  # Skip images smaller than 50x50
                            print(f"DEBUG: Skipping small image: {w}x{h}")
                            continue
                    except ValueError:
                        pass

                # Score images based on relevance (higher score = better)
                img_score = 0

                # High priority: tour-specific keywords in alt text
                tour_keywords = ['tour', 'trip', 'experience', 'adventure', 'travel', 'holiday', 'excursion', 'journey']
                if any(keyword in alt for keyword in tour_keywords):
                    img_score += 100
                    print(f"DEBUG: Tour-related alt text: {alt}")

                # High priority: tour-specific keywords in filename
                if any(keyword in src.lower() for keyword in tour_keywords):
                    img_score += 90
                    print(f"DEBUG: Tour-related filename: {src}")

                # Medium priority: larger images (if dimensions available)
                if width and height:
                    try:
                        w, h = int(width), int(height)
                        if w >= 300 and h >= 200:  # Good size for tour images
                            img_score += 50
                        elif w >= 200 and h >= 150:  # Decent size
                            img_score += 30
                    except ValueError:
                        pass

                # Medium priority: images in tour-specific containers
                parent_classes = ' '.join(img.parent.get('class', []) if img.parent else [])
                if any(keyword in parent_classes.lower() for keyword in ['tour', 'product', 'item', 'card']):
                    img_score += 40

                # Low priority: avoid generic/repeated images
                generic_indicators = ['header', 'banner', 'hero', 'main', 'site', 'global']
                if any(indicator in src.lower() for indicator in generic_indicators):
                    img_score -= 20
                if any(indicator in alt.lower() for indicator in generic_indicators):
                    img_score -= 20

                # Choose image with highest score
                if not best_img or img_score > getattr(best_img, '_score', 0):
                    best_img = img
                    best_img._score = img_score
                    print(f"DEBUG: New best image (score {img_score}): {src[:100]}...")

            if best_img:
                image_src = best_img.get('src')
                print(f"DEBUG: Found image for '{text}': {image_src}")
                # Make relative URLs absolute
                if image_src.startswith('/'):
                    from urllib.parse import urljoin
                    image_url = urljoin(base_url, image_src)
                elif image_src.startswith('http'):
                    image_url = image_src
                elif image_src.startswith('data:'):
                    # Skip data URLs (base64 encoded images)
                    image_url = None
                else:
                    # Relative URL without leading slash
                    from urllib.parse import urljoin
                    image_url = urljoin(base_url, image_src)
                print(f"DEBUG: Final image URL: {image_url}")

        # Fallback: If no image found via link association, look for featured images
        if not image_url:
            print("DEBUG: No image found via link association, trying fallback selectors")
            # Look for common featured image patterns
            featured_selectors = [
                'img[class*="featured"]',
                'img[class*="hero"]',
                'img[class*="banner"]',
                'img[class*="main"]',
                '.featured-image img',
                '.hero-image img',
                '.tour-image img',
                '.product-image img',
                '.post-thumbnail img',
                '.gallery img:first-child',
                '.slider img:first-child'
            ]

            # Same unwanted patterns as above
            unwanted_patterns = [
                'facebook.com/tr', 'google-analytics.com', 'googletagmanager.com',
                'doubleclick.net', 'googlesyndication.com', 'googleadservices.com',
                'twitter.com/i/adsct', 'linkedin.com/px', 'pinterest.com/v3/ct',
                'hotjar.com', 'crazyegg.com', 'mouseflow.com', 'fullstory.com',
                'icon', 'logo', 'avatar', 'thumb', 'pixel', 'tracking',
                'badge', 'button', 'arrow', 'spinner', 'loader',
                '.svg', '.gif', '1x1', 'spacer', 'blank',
                'facebook', 'twitter', 'instagram', 'youtube', 'linkedin',
                'social', 'share'
            ]

            for selector in featured_selectors:
                try:
                    featured_img = soup.select_one(selector)
                    if featured_img and featured_img.get('src'):
                        src = featured_img.get('src')

                        # Apply same filtering as above
                        if any(pattern in src.lower() for pattern in unwanted_patterns):
                            print(f"DEBUG: Fallback - skipping unwanted image: {src}")
                            continue

                        # Skip data URLs
                        if src.startswith('data:'):
                            continue

                        # Check image dimensions
                        width = featured_img.get('width')
                        height = featured_img.get('height')
                        if width and height:
                            try:
                                w, h = int(width), int(height)
                                if w < 50 or h < 50:
                                    print(f"DEBUG: Fallback - skipping small image: {w}x{h}")
                                    continue
                            except ValueError:
                                pass

                        # This looks like a valid image
                        if src.startswith('/'):
                            from urllib.parse import urljoin
                            image_url = urljoin(base_url, src)
                        elif src.startswith('http'):
                            image_url = src
                        else:
                            from urllib.parse import urljoin
                            image_url = urljoin(base_url, src)

                        print(f"DEBUG: Fallback found image: {image_url}")
                        break
                except Exception as e:
                    print(f"DEBUG: Error with selector {selector}: {e}")
                    continue

        found_tours.append(TourItem(
            title=text,
            location=location,
            start_date=None,
            end_date=None,
            price=price,
            url=href,
            image_url=image_url,
            last_updated=datetime.now(),
        ))

    print(f"DEBUG: Created {len(found_tours)} tour items")

    # Remove duplicates based on title
    seen_titles = set()
    for tour in found_tours:
        if tour.title not in seen_titles:
            seen_titles.add(tour.title)
            yield tour


