<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Business Directory</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            max-width: 1200px;
            margin: 0 auto;
            padding: 20px;
            background-color: #f5f5f5;
        }
        .header {
            background: white;
            padding: 20px;
            border-radius: 8px;
            margin-bottom: 20px;
            box-shadow: 0 2px 4px rgba(0,0,0,0.1);
        }
        .nav {
            margin-bottom: 20px;
        }
        .nav a {
            display: inline-block;
            padding: 10px 20px;
            background: #007bff;
            color: white;
            text-decoration: none;
            border-radius: 4px;
            margin-right: 10px;
        }
        .nav a:hover {
            background: #0056b3;
        }
        .business-card {
            background: white;
            border-radius: 8px;
            padding: 20px;
            margin-bottom: 15px;
            box-shadow: 0 2px 4px rgba(0,0,0,0.1);
            border-left: 4px solid #007bff;
            cursor: pointer;
            transition: all 0.3s ease;
        }
        .business-card:hover {
            box-shadow: 0 4px 8px rgba(0,0,0,0.15);
        }
        .business-card.inactive {
            border-left-color: #6c757d;
            opacity: 0.7;
        }
        .business-header {
            display: flex;
            justify-content: space-between;
            align-items: center;
            margin-bottom: 10px;
        }
        .business-title {
            display: flex;
            align-items: center;
            gap: 10px;
        }
        .expand-icon {
            font-size: 18px;
            transition: transform 0.3s ease;
            color: #007bff;
        }
        .expand-icon.expanded {
            transform: rotate(90deg);
        }
        .business-status {
            font-size: 12px;
            padding: 4px 8px;
            border-radius: 12px;
            font-weight: bold;
        }
        .status-active {
            background: #d4edda;
            color: #155724;
        }
        .status-inactive {
            background: #f8d7da;
            color: #721c24;
        }
        .tours-section {
            margin-top: 15px;
            padding-top: 15px;
            border-top: 1px solid #eee;
            display: none;
        }
        .tours-section.expanded {
            display: block;
        }
        .business-name {
            font-size: 18px;
            font-weight: bold;
            color: #333;
        }
        .business-id {
            background: #007bff;
            color: white;
            padding: 4px 12px;
            border-radius: 20px;
            font-size: 14px;
            font-weight: bold;
        }
        .business-url {
            color: #666;
            margin-bottom: 10px;
            word-break: break-all;
        }
        .business-url a {
            color: #007bff;
            text-decoration: none;
        }
        .business-url a:hover {
            text-decoration: underline;
        }
        .business-stats {
            display: flex;
            gap: 20px;
            margin-top: 10px;
        }
        .stat {
            display: flex;
            align-items: center;
            gap: 5px;
            color: #666;
            font-size: 14px;
        }
        .stat-number {
            background: #e9ecef;
            padding: 2px 8px;
            border-radius: 12px;
            font-weight: bold;
            color: #495057;
        }
        .business-meta {
            margin-top: 10px;
            font-size: 12px;
            color: #999;
        }
        .tours-section {
            margin-top: 15px;
            padding-top: 15px;
            border-top: 1px solid #eee;
        }
        .tours-header {
            display: flex;
            justify-content: space-between;
            align-items: center;
            margin-bottom: 10px;
        }
        .tours-header h4 {
            margin: 0;
            color: #333;
        }
        .tour-item {
            background: #f8f9fa;
            border-radius: 4px;
            padding: 10px;
            margin-bottom: 8px;
            display: flex;
            justify-content: space-between;
            align-items: center;
        }
        .tour-item.inactive {
            opacity: 0.6;
            background: #f1f1f1;
        }
        .tour-title {
            flex: 1;
            margin-right: 10px;
        }
        .tour-title.inactive {
            text-decoration: line-through;
            color: #666;
        }
        .tour-meta {
            font-size: 12px;
            color: #666;
            margin-right: 10px;
        }
        .btn-sm {
            padding: 4px 8px;
            font-size: 12px;
            border: none;
            border-radius: 3px;
            cursor: pointer;
        }
        .btn-toggle-active { background: #28a745; color: white; }
        .btn-toggle-inactive { background: #6c757d; color: white; }
        .btn-sm:hover { opacity: 0.8; }
        .search-box {
            background: white;
            padding: 20px;
            border-radius: 8px;
            margin-bottom: 20px;
            box-shadow: 0 2px 4px rgba(0,0,0,0.1);
        }
        .search-box input {
            width: 100%;
            padding: 10px;
            border: 1px solid #ddd;
            border-radius: 4px;
            font-size: 16px;
        }
        .stats-summary {
            background: white;
            padding: 20px;
            border-radius: 8px;
            margin-bottom: 20px;
            box-shadow: 0 2px 4px rgba(0,0,0,0.1);
        }
        .stats-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
            gap: 20px;
        }
        .stat-item {
            text-align: center;
        }
        .stat-item-number {
            font-size: 24px;
            font-weight: bold;
            color: #007bff;
        }
        .stat-item-label {
            color: #666;
            font-size: 14px;
        }
        .actions {
            margin-top: 15px;
            display: flex;
            gap: 10px;
            flex-wrap: wrap;
        }
        .btn {
            padding: 8px 16px;
            border: none;
            border-radius: 4px;
            cursor: pointer;
            font-size: 14px;
            text-decoration: none;
            display: inline-block;
        }
        .btn-primary { background: #007bff; color: white; }
        .btn-success { background: #28a745; color: white; }
        .btn-warning { background: #ffc107; color: #212529; }
        .btn-danger { background: #dc3545; color: white; }
        .btn:hover { opacity: 0.8; }
        .edit-form {
            display: none;
            margin-top: 15px;
            padding: 15px;
            background: #f8f9fa;
            border-radius: 4px;
        }
        .edit-form.show { display: block; }
        .form-group {
            margin-bottom: 15px;
        }
        .form-group label {
            display: block;
            margin-bottom: 5px;
            font-weight: bold;
        }
        .form-group input, .form-group select {
            width: 100%;
            padding: 8px;
            border: 1px solid #ddd;
            border-radius: 4px;
            font-size: 14px;
        }
    </style>
</head>
<body>
    <div class="header">
        <h1>Business Directory</h1>
        <div class="nav">
            <a href="/ui">Add Source</a>
            <a href="/ui/manage">Manage Sources</a>
            <a href="/ui/businesses">View Businesses</a>
            <a href="/api/businesses">API View</a>
        </div>
    </div>

    <div class="search-box">
        <input type="text" id="searchInput" placeholder="Search businesses by name or ID..." onkeyup="filterBusinesses()">
    </div>

    <div class="stats-summary">
        <div class="stats-grid">
            <div class="stat-item">
                <div class="stat-item-number">{{ business_data|length }}</div>
                <div class="stat-item-label">Total Businesses</div>
            </div>
            <div class="stat-item">
                <div class="stat-item-number">{{ business_data|sum(attribute='tour_count') }}</div>
                <div class="stat-item-label">Total Tours</div>
            </div>
            <div class="stat-item">
                <div class="stat-item-number">{{ business_data|sum(attribute='source_count') }}</div>
                <div class="stat-item-label">Total Sources</div>
            </div>
            <div class="stat-item">
                <div class="stat-item-number">{{ business_data|selectattr('tour_count', 'gt', 0)|list|length }}</div>
                <div class="stat-item-label">With Tours</div>
            </div>
        </div>
    </div>

    <div id="businessList">
        {% for data in business_data %}
        <div class="business-card {% if not data.is_active %}inactive{% endif %}" data-name="{{ data.business.name.lower() }}" data-id="{{ data.business.id }}" onclick="toggleBusiness({{ data.business.id }})">
            <div class="business-header">
                <div class="business-title">
                    <span class="expand-icon" id="icon-{{ data.business.id }}">▶</span>
                    <div class="business-name">{{ data.business.name }}</div>
                    <span class="business-status {% if data.is_active %}status-active{% else %}status-inactive{% endif %}">
                        {% if data.is_active %}Active{% else %}Inactive{% endif %}
                    </span>
                </div>
                <div class="business-id">ID: {{ data.business.id }}</div>
            </div>
            
            {% if data.business.website_url %}
            <div class="business-url">
                <a href="{{ data.business.website_url }}" target="_blank" rel="noopener">{{ data.business.website_url }}</a>
            </div>
            {% endif %}
            
            {% if data.business.description %}
            <div style="color: #666; margin-bottom: 10px;">{{ data.business.description }}</div>
            {% endif %}
            
            {% if data.business.category %}
            <div style="color: #666; margin-bottom: 10px;"><strong>Category:</strong> {{ data.business.category }}</div>
            {% endif %}
            
            <div class="business-stats">
                <div class="stat">
                    <span>Tours:</span>
                    <span class="stat-number">{{ data.active_tour_count }}/{{ data.tour_count }}</span>
                </div>
                <div class="stat">
                    <span>Sources:</span>
                    <span class="stat-number">{{ data.source_count }}</span>
                </div>
            </div>

            {% if data.tours %}
            <div class="tours-section" id="tours-{{ data.business.id }}">
                <div class="tours-header">
                    <h4>Tours ({{ data.active_tour_count }} active, {{ data.tour_count - data.active_tour_count }} inactive)</h4>
                </div>
                {% for tour in data.tours %}
                <div class="tour-item {% if not tour.is_active %}inactive{% endif %}">
                    {% if tour.image_url %}
                    <div style="position: relative; display: inline-block; margin-right: 10px;">
                        <img src="{{ tour.image_url }}" alt="{{ tour.title }}" style="width: 60px; height: 40px; object-fit: cover; border-radius: 4px;">
                        <form method="post" action="/ui/tours/{{ tour.id }}/remove-image" style="position: absolute; top: -5px; right: -5px;">
                            <button type="submit" onclick="return confirm('Remove this image?')"
                                    style="background: red; color: white; border: none; border-radius: 50%; width: 18px; height: 18px; font-size: 10px; cursor: pointer; padding: 0;">
                                ❌
                            </button>
                        </form>
                    </div>
                    {% endif %}
                    <div class="tour-title {% if not tour.is_active %}inactive{% endif %}">
                        {{ tour.title }}
                        {% if tour.price %}
                        <span class="tour-meta">${{ "%.0f"|format(tour.price) }}</span>
                        {% endif %}
                        {% if tour.location %}
                        <span class="tour-meta">{{ tour.location }}</span>
                        {% endif %}
                    </div>
                    <form method="post" action="/ui/businesses/{{ data.business.id }}/tours/{{ tour.id }}/toggle" style="display: inline;">
                        <button type="submit" class="btn-sm {% if tour.is_active %}btn-toggle-active{% else %}btn-toggle-inactive{% endif %}">
                            {% if tour.is_active %}✓ Active{% else %}✗ Hidden{% endif %}
                        </button>
                    </form>
                </div>
                {% endfor %}
            </div>
            {% endif %}

            <div class="business-meta">
                Created: {{ data.business.created_at.strftime('%Y-%m-%d %H:%M') if data.business.created_at else 'N/A' }}
            </div>
        </div>
        {% endfor %}
    </div>

    {% if not business_data %}
    <div class="business-card">
        <div class="business-name">No businesses found</div>
        <p>No businesses have been created yet. <a href="/ui">Add your first source</a> to get started.</p>
    </div>
    {% endif %}

    <script>
        function filterBusinesses() {
            const searchTerm = document.getElementById('searchInput').value.toLowerCase();
            const businessCards = document.querySelectorAll('.business-card');

            businessCards.forEach(card => {
                const name = card.getAttribute('data-name');
                const id = card.getAttribute('data-id');

                if (name.includes(searchTerm) || id.includes(searchTerm)) {
                    card.style.display = 'block';
                } else {
                    card.style.display = 'none';
                }
            });
        }

        function toggleBusiness(businessId) {
            const toursSection = document.getElementById('tours-' + businessId);
            const icon = document.getElementById('icon-' + businessId);

            if (toursSection.classList.contains('expanded')) {
                toursSection.classList.remove('expanded');
                icon.classList.remove('expanded');
                icon.textContent = '▶';
            } else {
                toursSection.classList.add('expanded');
                icon.classList.add('expanded');
                icon.textContent = '▼';
            }
        }

        // Prevent form submissions from triggering the toggle
        document.addEventListener('DOMContentLoaded', function() {
            const forms = document.querySelectorAll('.business-card form');
            forms.forEach(form => {
                form.addEventListener('click', function(e) {
                    e.stopPropagation();
                });
            });
        });
    </script>
</body>
</html>
