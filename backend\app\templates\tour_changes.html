<!DOCTYPE html>
<html>
<head>
    <title>Tour Changes</title>
    <style>
        body {
            font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
            margin: 0;
            padding: 20px;
            background-color: #f5f5f5;
        }
        .container {
            max-width: 1200px;
            margin: 0 auto;
            background: white;
            padding: 30px;
            border-radius: 8px;
            box-shadow: 0 2px 4px rgba(0,0,0,0.1);
        }
        .header {
            display: flex;
            justify-content: space-between;
            align-items: center;
            margin-bottom: 30px;
            padding-bottom: 20px;
            border-bottom: 2px solid #eee;
        }
        .nav-links {
            display: flex;
            gap: 15px;
        }
        .nav-links a {
            color: #007cba;
            text-decoration: none;
            padding: 8px 16px;
            border-radius: 4px;
            background: #f0f8ff;
        }
        .nav-links a:hover {
            background: #007cba;
            color: white;
        }
        .session-group {
            margin-bottom: 30px;
            border: 1px solid #ddd;
            border-radius: 8px;
            overflow: hidden;
        }
        .session-header {
            background: #f8f9fa;
            padding: 15px 20px;
            border-bottom: 1px solid #ddd;
            font-weight: bold;
            display: flex;
            justify-content: space-between;
            align-items: center;
        }
        .session-stats {
            font-size: 14px;
            color: #666;
        }
        .change-item {
            padding: 15px 20px;
            border-bottom: 1px solid #f0f0f0;
            display: flex;
            justify-content: space-between;
            align-items: center;
        }
        .change-item:last-child {
            border-bottom: none;
        }
        .change-info {
            flex: 1;
        }
        .tour-title {
            font-weight: bold;
            color: #333;
            margin-bottom: 5px;
        }
        .business-name {
            color: #666;
            font-size: 14px;
            margin-bottom: 8px;
        }
        .field-change {
            font-size: 14px;
        }
        .field-name {
            font-weight: bold;
            color: #007cba;
        }
        .change-values {
            margin-top: 5px;
        }
        .old-value {
            color: #dc3545;
            text-decoration: line-through;
        }
        .new-value {
            color: #28a745;
            font-weight: bold;
        }
        .change-type {
            padding: 4px 8px;
            border-radius: 4px;
            font-size: 12px;
            font-weight: bold;
            text-transform: uppercase;
        }
        .change-type.created {
            background: #d4edda;
            color: #155724;
        }
        .change-type.updated {
            background: #fff3cd;
            color: #856404;
        }
        .change-type.no_change {
            background: #f8f9fa;
            color: #6c757d;
        }
        .no-changes {
            text-align: center;
            padding: 40px;
            color: #666;
        }
        .filters {
            margin-bottom: 20px;
            display: flex;
            gap: 15px;
            align-items: center;
        }
        .filters select, .filters input {
            padding: 8px 12px;
            border: 1px solid #ddd;
            border-radius: 4px;
        }
        .clear-btn {
            background: #dc3545;
            color: white;
            border: none;
            padding: 8px 16px;
            border-radius: 4px;
            cursor: pointer;
        }
        .clear-btn:hover {
            background: #c82333;
        }
    </style>
</head>
<body>
    <div class="container">
        <div class="header">
            <h1>Tour Changes History</h1>
            <div class="nav-links">
                <a href="/ui/manage">← Manage Sources</a>
                <a href="/ui/businesses">View Businesses</a>
            </div>
        </div>

        <div class="filters">
            <select id="sessionFilter" onchange="filterChanges()">
                <option value="">All Sessions</option>
                {% for session in sessions %}
                <option value="{{ session }}">{{ session }}</option>
                {% endfor %}
            </select>
            
            <select id="typeFilter" onchange="filterChanges()">
                <option value="">All Change Types</option>
                <option value="created">Created</option>
                <option value="updated">Updated</option>
            </select>
            
            <input type="text" id="searchFilter" placeholder="Search tours..." onkeyup="filterChanges()">
            
            <form method="post" action="/ui/changes/clear" style="display: inline;">
                <button type="submit" class="clear-btn" onclick="return confirm('Clear all change history?')">
                    Clear History
                </button>
            </form>
        </div>

        {% if not change_sessions %}
        <div class="no-changes">
            <h3>No changes recorded yet</h3>
            <p>Run "Scrape All Active Sources" to start tracking changes.</p>
        </div>
        {% else %}
        {% for session, changes in change_sessions.items() %}
        <div class="session-group" data-session="{{ session }}">
            <div class="session-header">
                <span>Scrape Session: {{ session }}</span>
                <span class="session-stats">
                    {{ changes|length }} changes • 
                    {{ changes|selectattr("change_type", "equalto", "created")|list|length }} created • 
                    {{ changes|selectattr("change_type", "equalto", "updated")|list|length }} updated
                </span>
            </div>
            
            {% for change in changes %}
            <div class="change-item" data-type="{{ change.change_type }}" data-tour="{{ change.tour_title.lower() }}">
                <div class="change-info">
                    <div class="tour-title">{{ change.tour_title }}</div>
                    <div class="business-name">{{ change.business_name }}</div>
                    <div class="field-change">
                        <span class="field-name">{{ change.field_name.replace('_', ' ').title() }}:</span>
                        {% if change.change_type == 'created' %}
                            <span class="new-value">{{ change.new_value or 'N/A' }}</span>
                        {% elif change.old_value != change.new_value %}
                            <div class="change-values">
                                <span class="old-value">{{ change.old_value or 'None' }}</span>
                                →
                                <span class="new-value">{{ change.new_value or 'None' }}</span>
                            </div>
                        {% else %}
                            <span>No change</span>
                        {% endif %}
                    </div>
                </div>
                <div class="change-type {{ change.change_type }}">
                    {{ change.change_type }}
                </div>
            </div>
            {% endfor %}
        </div>
        {% endfor %}
        {% endif %}
    </div>

    <script>
        function filterChanges() {
            const sessionFilter = document.getElementById('sessionFilter').value;
            const typeFilter = document.getElementById('typeFilter').value;
            const searchFilter = document.getElementById('searchFilter').value.toLowerCase();
            
            const sessionGroups = document.querySelectorAll('.session-group');
            
            sessionGroups.forEach(group => {
                const session = group.getAttribute('data-session');
                let groupVisible = false;
                
                // Check if session matches filter
                if (sessionFilter && session !== sessionFilter) {
                    group.style.display = 'none';
                    return;
                }
                
                const changeItems = group.querySelectorAll('.change-item');
                changeItems.forEach(item => {
                    const type = item.getAttribute('data-type');
                    const tour = item.getAttribute('data-tour');
                    
                    const typeMatch = !typeFilter || type === typeFilter;
                    const searchMatch = !searchFilter || tour.includes(searchFilter);
                    
                    if (typeMatch && searchMatch) {
                        item.style.display = 'flex';
                        groupVisible = true;
                    } else {
                        item.style.display = 'none';
                    }
                });
                
                group.style.display = groupVisible ? 'block' : 'none';
            });
        }
    </script>
</body>
</html>
