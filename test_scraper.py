#!/usr/bin/env python3
"""
Test script to debug the scraper on real websites
"""
import asyncio
import sys
import os

# Add the app directory to the path
sys.path.insert(0, '/app')

from app.worker.scraper import fetch_html, parse_example

async def test_scraper():
    """Test the scraper on SA Eco Tours website"""
    url = "https://saecotours.com.au/tours"
    
    print(f"Testing scraper on: {url}")
    print("=" * 50)
    
    try:
        # Fetch the HTML
        print("1. Fetching HTML...")
        html = await fetch_html(url)
        print(f"   HTML length: {len(html)} characters")
        
        # Test the content filter first
        print("\n2. Testing content filter...")
        import re

        def _contains_tour_content(html: str) -> bool:
            """Check if HTML contains tour-related content."""
            html_lower = html.lower()

            # Look for tour-related keywords
            tour_keywords = [
                'tour', 'tours', 'experience', 'adventure', 'excursion', 'package',
                'activity', 'trip', 'journey', 'expedition', 'booking', 'book now'
            ]

            # Must have multiple tour keywords
            keyword_count = sum(1 for keyword in tour_keywords if keyword in html_lower)

            # Look for pricing indicators
            price_patterns = [
                r'\$\d+', r'from\s*\$', r'price', r'cost', r'aud', r'per person'
            ]
            has_pricing = any(re.search(pattern, html_lower) for pattern in price_patterns)

            # Look for booking/contact elements
            booking_indicators = ['book', 'reserve', 'contact', 'enquire', 'phone', 'email']
            has_booking = any(indicator in html_lower for indicator in booking_indicators)

            print(f"   Keyword count: {keyword_count}")
            print(f"   Has pricing: {has_pricing}")
            print(f"   Has booking: {has_booking}")

            return keyword_count >= 3 and (has_pricing or has_booking)

        content_valid = _contains_tour_content(html)
        print(f"   Content filter result: {content_valid}")

        # Test the parser
        print("\n3. Running parser...")
        tours = list(parse_example(html, url))
        print(f"   Found {len(tours)} tours")
        
        if tours:
            print("\n4. Tour details:")
            for i, tour in enumerate(tours, 1):
                print(f"   Tour {i}:")
                print(f"     Title: {tour.title}")
                print(f"     Location: {tour.location}")
                print(f"     Price: {tour.price}")
                print(f"     URL: {tour.url}")
                print()

            # Test price extraction on the full page
            print("\n5. Testing price extraction on full page:")
            import re
            price_patterns = [
                r'\$\d+(?:,\d{3})*(?:\.\d{2})?',
                r'from\s*\$\d+',
                r'\d+\s*AUD',
                r'Price:\s*\$\d+',
                r'AUD\s*\$?\d+',
            ]

            found_prices = []
            for pattern in price_patterns:
                matches = re.findall(pattern, html, re.IGNORECASE)
                if matches:
                    found_prices.extend(matches)
                    print(f"   Pattern '{pattern}' found: {matches[:5]}")  # Show first 5 matches

            if found_prices:
                print(f"   Total price-like text found: {len(found_prices)}")
                print(f"   Examples: {found_prices[:10]}")
            else:
                print("   No price patterns found on the page")
        else:
            print("\n4. No tours found. Let's debug...")
            
            # Look for common patterns in the HTML
            html_lower = html.lower()
            
            # Check for tour-related content
            tour_keywords = ['tour', 'experience', 'adventure', 'trip', 'excursion', 'package', 'activity']
            found_keywords = [kw for kw in tour_keywords if kw in html_lower]
            print(f"   Found keywords: {found_keywords}")
            
            # Look for structured data
            if 'application/ld+json' in html:
                print("   Found JSON-LD structured data")
            else:
                print("   No JSON-LD structured data found")
            
            # Look for common selectors
            from bs4 import BeautifulSoup
            soup = BeautifulSoup(html, 'html.parser')
            
            selectors_to_check = [
                '.elementor-widget-container',
                '.jet-listing-grid__item', 
                '.tour-item',
                '.tour-card',
                '.package-item',
                'article',
                '.post',
                '.entry',
                'h1', 'h2', 'h3', 'h4'
            ]
            
            print("\n   Checking selectors:")
            for selector in selectors_to_check:
                elements = soup.select(selector)
                if elements:
                    print(f"     {selector}: {len(elements)} elements")
                    # Show first few elements
                    for i, elem in enumerate(elements[:3]):
                        text = elem.get_text(strip=True)[:100]
                        if text:
                            print(f"       [{i+1}] {text}...")
            
            # Look for headings that might be tours
            print("\n   All headings with tour keywords:")
            for tag in ['h1', 'h2', 'h3', 'h4', 'h5', 'h6']:
                headings = soup.find_all(tag)
                for heading in headings:
                    text = heading.get_text(strip=True)
                    if any(kw in text.lower() for kw in tour_keywords) and len(text) > 10:
                        print(f"     {tag.upper()}: {text}")

            # Look for links that might be tours
            print("\n   Links with tour keywords:")
            links = soup.find_all('a', href=True)
            tour_links = []
            for link in links:
                text = link.get_text(strip=True)
                href = link.get('href', '')
                if any(kw in text.lower() for kw in tour_keywords) and len(text) > 10 and len(text) < 100:
                    tour_links.append((text, href))

            for text, href in tour_links[:10]:  # Show first 10
                print(f"     LINK: {text} -> {href}")

            print(f"\n   Total potential tour links found: {len(tour_links)}")
    
    except Exception as e:
        print(f"Error: {e}")
        import traceback
        traceback.print_exc()

if __name__ == "__main__":
    asyncio.run(test_scraper())
